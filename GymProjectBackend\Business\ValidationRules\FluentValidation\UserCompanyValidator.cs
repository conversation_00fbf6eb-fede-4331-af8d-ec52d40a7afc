﻿using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class UserCompanyValidator : AbstractValidator<UserCompany>
    {
        private readonly IUserCompanyDal _userCompanyDal;

        public UserCompanyValidator()
        {
            _userCompanyDal = ServiceTool.ServiceProvider?.GetService<IUserCompanyDal>();

            RuleFor(p => p.UserID).NotEmpty().WithMessage("Kullanıcı seçimi boş bırakılamaz.");
            RuleFor(p => p.CompanyId).NotEmpty().WithMessage("Şirket seçimi boş bırakılamaz.");
            RuleFor(p => p).Must(BeUniqueCompanyName).WithMessage("Bu şirket zaten bir kullanıcıya bağlı!");
        }

        private bool BeUniqueCompanyName(UserCompany userCompany)
        {
            // Null check for DI services
            if (_userCompanyDal == null)
                return true; // Validation geçer, başka katmanda kontrol edilir

            // DI kullanılıyor - Scalability optimized
            if (userCompany.UserCompanyID != 0)
            {
                // Güncelleme durumu
                return !_userCompanyDal.GetAll(uc =>
                    uc.CompanyId == userCompany.CompanyId &&
                    uc.UserCompanyID != userCompany.UserCompanyID &&
                    uc.IsActive == true).Any();
            }
            else
            {
                // Yeni ekleme durumu
                return !_userCompanyDal.GetAll(uc =>
                    uc.CompanyId == userCompany.CompanyId &&
                    uc.IsActive == true).Any();
            }
        }
    }
}