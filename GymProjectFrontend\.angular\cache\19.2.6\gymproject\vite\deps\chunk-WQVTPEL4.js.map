{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/portal-directives-dced6d68.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/overlay-module-1d184db0.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/overlay.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  _attachedHost;\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  /** The type of the component that will be instantiated for attachment. */\n  component;\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef;\n  /** Injector used for the instantiation of the component. */\n  injector;\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n   */\n  projectableNodes;\n  constructor(component, viewContainerRef, injector,\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  _componentFactoryResolver, projectableNodes) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  templateRef;\n  viewContainerRef;\n  context;\n  injector;\n  constructor(/** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  /** DOM node hosting the portal's content. */\n  element;\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  /** The portal currently attached to the host. */\n  _attachedPortal;\n  /** A function that will permanently dispose this host. */\n  _disposeFn;\n  /** Whether this host has already been permanently disposed. */\n  _isDisposed = false;\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n  attachDomPortal = null;\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  outletElement;\n  _appRef;\n  _defaultInjector;\n  _document;\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _unusedComponentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(/** Element into which the content is projected. */\n  outletElement,\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  _unusedComponentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      const injector = portal.injector || portal.viewContainerRef.injector;\n      const ngModuleRef = injector.get(NgModuleRef, null, {\n        optional: true\n      }) || undefined;\n      componentRef = portal.viewContainerRef.createComponent(portal.component, {\n        index: portal.viewContainerRef.length,\n        injector,\n        ngModuleRef,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      const appRef = this._appRef;\n      const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n      const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n      componentRef = createComponent(portal.component, {\n        elementInjector,\n        environmentInjector,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (appRef.viewCount > 0) {\n          appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Attaches a DOM portal by transferring its content into the outlet.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n    element.parentNode.insertBefore(anchorNode, element);\n    this.outletElement.appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      // We can't use `replaceWith` here because IE doesn't support it.\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    super(templateRef, viewContainerRef);\n  }\n  static ɵfac = function CdkPortal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkPortal)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortal,\n    selectors: [[\"\", \"cdkPortal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal'\n    }]\n  }], () => [], null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTemplatePortalDirective_BaseFactory;\n    return function TemplatePortalDirective_Factory(__ngFactoryType__) {\n      return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(__ngFactoryType__ || TemplatePortalDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TemplatePortalDirective,\n    selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortal,\n      useExisting: TemplatePortalDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n  _moduleRef = inject(NgModuleRef, {\n    optional: true\n  });\n  _document = inject(DOCUMENT);\n  _viewContainerRef = inject(ViewContainerRef);\n  /** Whether the portal component is initialized. */\n  _isInitialized = false;\n  /** Reference to the currently-attached component/view ref. */\n  _attachedRef;\n  constructor() {\n    super();\n  }\n  /** Portal associated with the Portal outlet. */\n  get portal() {\n    return this._attachedPortal;\n  }\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n    if (this.hasAttached()) {\n      super.detach();\n    }\n    if (portal) {\n      super.attach(portal);\n    }\n    this._attachedPortal = portal || null;\n  }\n  /** Emits when a portal is attached to the outlet. */\n  attached = new EventEmitter();\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef() {\n    return this._attachedRef;\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this);\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const ref = viewContainerRef.createComponent(portal.component, {\n      index: viewContainerRef.length,\n      injector: portal.injector || viewContainerRef.injector,\n      projectableNodes: portal.projectableNodes || undefined,\n      ngModuleRef: this._moduleRef || undefined\n    });\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /**\n   * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n    portal.setAttachedHost(this);\n    element.parentNode.insertBefore(anchorNode, element);\n    this._getRootNode().appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /** Gets the root node of the portal outlet. */\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement;\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n  static ɵfac = function CdkPortalOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkPortalOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortalOutlet,\n    selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n    inputs: {\n      portal: [0, \"cdkPortalOutlet\", \"portal\"]\n    },\n    outputs: {\n      attached: \"attached\"\n    },\n    exportAs: [\"cdkPortalOutlet\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet'\n    }]\n  }], () => [], {\n    portal: [{\n      type: Input,\n      args: ['cdkPortalOutlet']\n    }],\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵPortalHostDirective_BaseFactory;\n    return function PortalHostDirective_Factory(__ngFactoryType__) {\n      return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(__ngFactoryType__ || PortalHostDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PortalHostDirective,\n    selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n    inputs: {\n      portal: [0, \"cdkPortalHost\", \"portal\"]\n    },\n    exportAs: [\"cdkPortalHost\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortalOutlet,\n      useExisting: PortalHostDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: [{\n        name: 'portal',\n        alias: 'cdkPortalHost'\n      }],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]\n    }]\n  }], null, null);\n})();\nclass PortalModule {\n  static ɵfac = function PortalModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PortalModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PortalModule,\n    imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\nexport { BasePortalOutlet as B, CdkPortalOutlet as C, DomPortalOutlet as D, PortalModule as P, TemplatePortal as T, ComponentPortal as a, CdkPortal as b, TemplatePortalDirective as c, PortalHostDirective as d, Portal as e, DomPortal as f, BasePortalHost as g, DomPortalHost as h };\n", "import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, untracked, afterRender, afterNextRender, ElementRef, Injector, ANIMATION_MODULE_TYPE, EnvironmentInjector, ApplicationRef, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT, Location } from '@angular/common';\nimport { P as Platform } from './platform-20fc4de8.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-08253a84.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-318658ae.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-f6f8bc13.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-09eecacc.mjs';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport { c as coerceCssPixelValue } from './css-pixel-value-5d0cae55.mjs';\nimport { c as coerceArray } from './array-6239d2f8.mjs';\nimport { S as ScrollDispatcher, V as ViewportRuler, a as ScrollingModule } from './scrolling-module-722545e3.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-59340c46.mjs';\nimport { D as DomPortalOutlet, T as TemplatePortal, P as PortalModule } from './portal-directives-dced6d68.mjs';\nimport { D as Directionality } from './directionality-9d44e426.mjs';\nimport { _ as _IdGenerator } from './id-generator-0b91c6f7.mjs';\nimport { e as ESCAPE } from './keycodes-0e4398c6.mjs';\nimport { h as hasModifierKey } from './modifiers-3e8908bb.mjs';\nimport { B as BidiModule } from './bidi-module-04c03e58.mjs';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  _viewportRuler;\n  _previousHTMLStyles = {\n    top: '',\n    left: ''\n  };\n  _previousScrollPosition;\n  _isEnabled = false;\n  _document;\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  _scrollDispatcher;\n  _ngZone;\n  _viewportRuler;\n  _config;\n  _scrollSubscription = null;\n  _overlayRef;\n  _initialScrollPosition;\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  _detach = () => {\n    this.disable();\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  };\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  _scrollDispatcher;\n  _viewportRuler;\n  _ngZone;\n  _config;\n  _scrollSubscription = null;\n  _overlayRef;\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  _scrollDispatcher = inject(ScrollDispatcher);\n  _viewportRuler = inject(ViewportRuler);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  constructor() {}\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n  /** Block scrolling. */\n  block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n  static ɵfac = function ScrollStrategyOptions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScrollStrategyOptions)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ScrollStrategyOptions,\n    factory: ScrollStrategyOptions.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy;\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy = new NoopScrollStrategy();\n  /** Custom class to add to the overlay pane. */\n  panelClass = '';\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop = false;\n  /** Custom class to add to the backdrop */\n  backdropClass = 'cdk-overlay-dark-backdrop';\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width;\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height;\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth;\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight;\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth;\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight;\n  /**\n   * Direction of the text in the overlay panel. If a `Directionality` instance\n   * is passed in, the overlay will handle changes to its value automatically.\n   */\n  direction;\n  /**\n   * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  disposeOnNavigation = false;\n  constructor(config) {\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  offsetX;\n  offsetY;\n  panelClass;\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY;\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n  isOriginClipped;\n  isOriginOutsideView;\n  isOverlayClipped;\n  isOverlayOutsideView;\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  connectionPair;\n  scrollableViewProperties;\n  constructor(/** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays = [];\n  _document = inject(DOCUMENT);\n  _isAttached;\n  constructor() {}\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n  static ɵfac = function BaseOverlayDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseOverlayDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BaseOverlayDispatcher,\n    factory: BaseOverlayDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cleanupKeydown;\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n      });\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._cleanupKeydown?.();\n      this._isAttached = false;\n    }\n  }\n  /** Keyboard event listener that will be attached to the body. */\n  _keydownListener = event => {\n    const overlays = this._attachedOverlays;\n    for (let i = overlays.length - 1; i > -1; i--) {\n      // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n      // We want to target the most recent overlay, rather than trying to match where the event came\n      // from, because some components might open an overlay, but keep focus on a trigger element\n      // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n      // because we don't want overlays that don't handle keyboard events to block the ones below\n      // them that do.\n      if (overlays[i]._keydownEvents.observers.length > 0) {\n        this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n        break;\n      }\n    }\n  };\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayKeyboardDispatcher_BaseFactory;\n    return function OverlayKeyboardDispatcher_Factory(__ngFactoryType__) {\n      return (ɵOverlayKeyboardDispatcher_BaseFactory || (ɵOverlayKeyboardDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayKeyboardDispatcher)))(__ngFactoryType__ || OverlayKeyboardDispatcher);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayKeyboardDispatcher,\n    factory: OverlayKeyboardDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cursorOriginalValue;\n  _cursorStyleIsSet = false;\n  _pointerDownEventTarget;\n  _cleanups;\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      const eventOptions = {\n        capture: true\n      };\n      this._cleanups = this._ngZone.runOutsideAngular(() => [_bindEventWithOptions(this._renderer, body, 'pointerdown', this._pointerDownListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'click', this._clickListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'auxclick', this._clickListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'contextmenu', this._clickListener, eventOptions)]);\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._cleanups?.forEach(cleanup => cleanup());\n      this._cleanups = undefined;\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        this._document.body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  /** Store pointerdown event target to track origin of click. */\n  _pointerDownListener = event => {\n    this._pointerDownEventTarget = _getEventTarget(event);\n  };\n  /** Click event listener that will be attached to the body propagate phase. */\n  _clickListener = event => {\n    const target = _getEventTarget(event);\n    // In case of a click event, we want to check the origin of the click\n    // (e.g. in case where a user starts a click inside the overlay and\n    // releases the click outside of it).\n    // This is done by using the event target of the preceding pointerdown event.\n    // Every click event caused by a pointer device has a preceding pointerdown\n    // event, unless the click was programmatically triggered (e.g. in a unit test).\n    const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n    // Reset the stored pointerdown event target, to avoid having it interfere\n    // in subsequent events.\n    this._pointerDownEventTarget = null;\n    // We copy the array because the original may be modified asynchronously if the\n    // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n    // the for loop.\n    const overlays = this._attachedOverlays.slice();\n    // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n    // We want to target all overlays for which the click could be considered as outside click.\n    // As soon as we reach an overlay for which the click is not outside click we break off\n    // the loop.\n    for (let i = overlays.length - 1; i > -1; i--) {\n      const overlayRef = overlays[i];\n      if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n        continue;\n      }\n      // If it's a click inside the overlay, just break - we should do nothing\n      // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n      // and proceed with the next overlay\n      if (containsPierceShadowDom(overlayRef.overlayElement, target) || containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n        break;\n      }\n      const outsidePointerEvents = overlayRef._outsidePointerEvents;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.run(() => outsidePointerEvents.next(event));\n      } else {\n        outsidePointerEvents.next(event);\n      }\n    }\n  };\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayOutsideClickDispatcher_BaseFactory;\n    return function OverlayOutsideClickDispatcher_Factory(__ngFactoryType__) {\n      return (ɵOverlayOutsideClickDispatcher_BaseFactory || (ɵOverlayOutsideClickDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayOutsideClickDispatcher)))(__ngFactoryType__ || OverlayOutsideClickDispatcher);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayOutsideClickDispatcher,\n    factory: OverlayOutsideClickDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current = child;\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n    current = supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n  return false;\n}\nclass _CdkOverlayStyleLoader {\n  static ɵfac = function _CdkOverlayStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkOverlayStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _CdkOverlayStyleLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-overlay-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _CdkOverlayStyleLoader_Template(rf, ctx) {},\n    styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkOverlayStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-overlay-style-loader': ''\n      },\n      styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\"]\n    }]\n  }], null, null);\n})();\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  _platform = inject(Platform);\n  _containerElement;\n  _document = inject(DOCUMENT);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  constructor() {}\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    this._loadStyles();\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  /** Loads the structural styles necessary for the overlay to work. */\n  _loadStyles() {\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n  }\n  static ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayContainer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayContainer,\n    factory: OverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n  _renderer;\n  _ngZone;\n  element;\n  _cleanupClick;\n  _cleanupTransitionEnd;\n  _fallbackTimeout;\n  constructor(document, _renderer, _ngZone, onClick) {\n    this._renderer = _renderer;\n    this._ngZone = _ngZone;\n    this.element = document.createElement('div');\n    this.element.classList.add('cdk-overlay-backdrop');\n    this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n  }\n  detach() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this.element;\n      clearTimeout(this._fallbackTimeout);\n      this._cleanupTransitionEnd?.();\n      this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n      this._fallbackTimeout = setTimeout(this.dispose, 500);\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      element.style.pointerEvents = 'none';\n      element.classList.remove('cdk-overlay-backdrop-showing');\n    });\n  }\n  dispose = () => {\n    clearTimeout(this._fallbackTimeout);\n    this._cleanupClick?.();\n    this._cleanupTransitionEnd?.();\n    this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n    this.element.remove();\n  };\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  _portalOutlet;\n  _host;\n  _pane;\n  _config;\n  _ngZone;\n  _keyboardDispatcher;\n  _document;\n  _location;\n  _outsideClickDispatcher;\n  _animationsDisabled;\n  _injector;\n  _renderer;\n  _backdropClick = new Subject();\n  _attachments = new Subject();\n  _detachments = new Subject();\n  _positionStrategy;\n  _scrollStrategy;\n  _locationChanges = Subscription.EMPTY;\n  _backdropRef = null;\n  /**\n   * Reference to the parent of the `_host` at the time it was detached. Used to restore\n   * the `_host` to its original position in the DOM when it gets re-attached.\n   */\n  _previousHostParent;\n  /** Stream of keydown events dispatched to this overlay. */\n  _keydownEvents = new Subject();\n  /** Stream of mouse outside events dispatched to this overlay. */\n  _outsidePointerEvents = new Subject();\n  _renders = new Subject();\n  _afterRenderRef;\n  /** Reference to the currently-running `afterNextRender` call. */\n  _afterNextRenderRef;\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._injector = _injector;\n    this._renderer = _renderer;\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n    // Users could open the overlay from an `effect`, in which case we need to\n    // run the `afterRender` as `untracked`. We don't recommend that users do\n    // this, but we also don't want to break users who are doing it.\n    this._afterRenderRef = untracked(() => afterRender(() => {\n      this._renders.next();\n    }, {\n      injector: this._injector\n    }));\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropRef?.element || null;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    this._afterNextRenderRef?.destroy();\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(() => {\n      // The overlay could've been detached before the callback executed.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }, {\n      injector: this._injector\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._backdropRef?.dispose();\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._afterNextRenderRef?.destroy();\n    this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n    this._afterRenderRef.destroy();\n    this._renders.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropRef?.dispose();\n    this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n      this._backdropClick.next(event);\n    });\n    if (this._animationsDisabled) {\n      this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n      });\n    } else {\n      this._backdropRef.element.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    if (this._animationsDisabled) {\n      this._backdropRef?.dispose();\n      this._backdropRef = null;\n    } else {\n      this._backdropRef?.detach();\n    }\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenEmpty() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._renders.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    scrollStrategy?.disable();\n    scrollStrategy?.detach?.();\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  _viewportRuler;\n  _document;\n  _platform;\n  _overlayContainer;\n  /** The overlay to which this strategy is attached. */\n  _overlayRef;\n  /** Whether we're performing the very first positioning of the overlay. */\n  _isInitialRender;\n  /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n  _lastBoundingBoxSize = {\n    width: 0,\n    height: 0\n  };\n  /** Whether the overlay was pushed in a previous positioning. */\n  _isPushed = false;\n  /** Whether the overlay can be pushed on-screen on the initial open. */\n  _canPush = true;\n  /** Whether the overlay can grow via flexible width/height after the initial open. */\n  _growAfterOpen = false;\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  _hasFlexibleDimensions = true;\n  /** Whether the overlay position is locked. */\n  _positionLocked = false;\n  /** Cached origin dimensions */\n  _originRect;\n  /** Cached overlay dimensions */\n  _overlayRect;\n  /** Cached viewport dimensions */\n  _viewportRect;\n  /** Cached container dimensions */\n  _containerRect;\n  /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n  _viewportMargin = 0;\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  _scrollables = [];\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions = [];\n  /** The origin element against which the overlay will be positioned. */\n  _origin;\n  /** The overlay pane element. */\n  _pane;\n  /** Whether the strategy has been disposed of already. */\n  _isDisposed;\n  /**\n   * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n   * within the viewport.\n   */\n  _boundingBox;\n  /** The last position to have been calculated as the best fit position. */\n  _lastPosition;\n  /** The last calculated scroll visibility. Only tracked  */\n  _lastScrollVisibility;\n  /** Subject that emits whenever the position changes. */\n  _positionChanges = new Subject();\n  /** Subscription to viewport size changes. */\n  _resizeSubscription = Subscription.EMPTY;\n  /** Default offset for the overlay along the x axis. */\n  _offsetX = 0;\n  /** Default offset for the overlay along the y axis. */\n  _offsetY = 0;\n  /** Selector to be used when finding the elements on which to set the transform origin. */\n  _transformOriginSelector;\n  /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n  _appliedPanelClasses = [];\n  /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n  _previousPushAmount;\n  /** Observable sequence of position changes. */\n  positionChanges = this._positionChanges;\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (position !== this._lastPosition || !this._lastScrollVisibility || !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n      this._lastScrollVisibility = scrollVisibility;\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the DOMRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n  if (a === b) {\n    return true;\n  }\n  return a.isOriginClipped === b.isOriginClipped && a.isOriginOutsideView === b.isOriginOutsideView && a.isOverlayClipped === b.isOverlayClipped && a.isOverlayOutsideView === b.isOverlayOutsideView;\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  _overlayRef;\n  _cssPosition = 'static';\n  _topOffset = '';\n  _bottomOffset = '';\n  _alignItems = '';\n  _xPosition = '';\n  _xOffset = '';\n  _width = '';\n  _height = '';\n  _isDisposed = false;\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  _viewportRuler = inject(ViewportRuler);\n  _document = inject(DOCUMENT);\n  _platform = inject(Platform);\n  _overlayContainer = inject(OverlayContainer);\n  constructor() {}\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n  static ɵfac = function OverlayPositionBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayPositionBuilder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayPositionBuilder,\n    factory: OverlayPositionBuilder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  scrollStrategies = inject(ScrollStrategyOptions);\n  _overlayContainer = inject(OverlayContainer);\n  _positionBuilder = inject(OverlayPositionBuilder);\n  _keyboardDispatcher = inject(OverlayKeyboardDispatcher);\n  _injector = inject(Injector);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _directionality = inject(Directionality);\n  _location = inject(Location);\n  _outsideClickDispatcher = inject(OverlayOutsideClickDispatcher);\n  _animationsModuleType = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _appRef;\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  constructor() {}\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    // This is done in the overlay container as well, but we have it here\n    // since it's common to mock out the overlay container in tests.\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector), this._renderer);\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = this._idGenerator.getId('cdk-overlay-');\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, null, this._appRef, this._injector, this._document);\n  }\n  static ɵfac = function Overlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Overlay)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Overlay,\n    factory: Overlay.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  elementRef = inject(ElementRef);\n  constructor() {}\n  static ɵfac = function CdkOverlayOrigin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkOverlayOrigin)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkOverlayOrigin,\n    selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n    exportAs: [\"cdkOverlayOrigin\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  _overlay = inject(Overlay);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _overlayRef;\n  _templatePortal;\n  _backdropSubscription = Subscription.EMPTY;\n  _attachSubscription = Subscription.EMPTY;\n  _detachSubscription = Subscription.EMPTY;\n  _positionSubscription = Subscription.EMPTY;\n  _offsetX;\n  _offsetY;\n  _position;\n  _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n  _disposeOnNavigation = false;\n  _ngZone = inject(NgZone);\n  /** Origin for the connected overlay. */\n  origin;\n  /** Registered connected position pairs. */\n  positions;\n  /**\n   * This input overrides the positions input if specified. It lets users pass\n   * in arbitrary positioning strategies.\n   */\n  positionStrategy;\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The width of the overlay panel. */\n  width;\n  /** The height of the overlay panel. */\n  height;\n  /** The min width of the overlay panel. */\n  minWidth;\n  /** The min height of the overlay panel. */\n  minHeight;\n  /** The custom class to be set on the backdrop element. */\n  backdropClass;\n  /** The custom class to add to the overlay pane element. */\n  panelClass;\n  /** Margin between the overlay and the viewport edges. */\n  viewportMargin = 0;\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy;\n  /** Whether the overlay is open. */\n  open = false;\n  /** Whether the overlay can be closed by user interaction. */\n  disableClose = false;\n  /** CSS selector which to set the transform origin. */\n  transformOriginSelector;\n  /** Whether or not the overlay should attach a backdrop. */\n  hasBackdrop = false;\n  /** Whether or not the overlay should be locked when scrolling. */\n  lockPosition = false;\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  flexibleDimensions = false;\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  growAfterOpen = false;\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  push = false;\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  get disposeOnNavigation() {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value) {\n    this._disposeOnNavigation = value;\n  }\n  /** Event emitted when the backdrop is clicked. */\n  backdropClick = new EventEmitter();\n  /** Event emitted when the position has changed. */\n  positionChange = new EventEmitter();\n  /** Event emitted when the overlay has been attached. */\n  attach = new EventEmitter();\n  /** Event emitted when the overlay has been detached. */\n  detach = new EventEmitter();\n  /** Emits when there are keyboard events that are targeted at the overlay. */\n  overlayKeydown = new EventEmitter();\n  /** Emits when there are mouse outside click events that are targeted at the overlay. */\n  overlayOutsideClick = new EventEmitter();\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this._overlayRef?.dispose();\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef?.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this.attachOverlay() : this.detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event);\n      if (!origin || origin !== target && !origin.contains(target)) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir || 'ltr',\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  _getOriginElement() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n    return null;\n  }\n  /** Attaches the overlay. */\n  attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this._ngZone.run(() => this.positionChange.emit(position));\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n    this.open = true;\n  }\n  /** Detaches the overlay. */\n  detachOverlay() {\n    this._overlayRef?.detach();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this.open = false;\n  }\n  static ɵfac = function CdkConnectedOverlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkConnectedOverlay)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkConnectedOverlay,\n    selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n    inputs: {\n      origin: [0, \"cdkConnectedOverlayOrigin\", \"origin\"],\n      positions: [0, \"cdkConnectedOverlayPositions\", \"positions\"],\n      positionStrategy: [0, \"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n      offsetX: [0, \"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n      offsetY: [0, \"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n      width: [0, \"cdkConnectedOverlayWidth\", \"width\"],\n      height: [0, \"cdkConnectedOverlayHeight\", \"height\"],\n      minWidth: [0, \"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n      minHeight: [0, \"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n      backdropClass: [0, \"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n      panelClass: [0, \"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n      viewportMargin: [0, \"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n      scrollStrategy: [0, \"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n      open: [0, \"cdkConnectedOverlayOpen\", \"open\"],\n      disableClose: [0, \"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n      transformOriginSelector: [0, \"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n      hasBackdrop: [2, \"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute],\n      lockPosition: [2, \"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute],\n      flexibleDimensions: [2, \"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute],\n      growAfterOpen: [2, \"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute],\n      push: [2, \"cdkConnectedOverlayPush\", \"push\", booleanAttribute],\n      disposeOnNavigation: [2, \"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute]\n    },\n    outputs: {\n      backdropClick: \"backdropClick\",\n      positionChange: \"positionChange\",\n      attach: \"attach\",\n      detach: \"detach\",\n      overlayKeydown: \"overlayKeydown\",\n      overlayOutsideClick: \"overlayOutsideClick\"\n    },\n    exportAs: [\"cdkConnectedOverlay\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay'\n    }]\n  }], () => [], {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayHasBackdrop',\n        transform: booleanAttribute\n      }]\n    }],\n    lockPosition: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayLockPosition',\n        transform: booleanAttribute\n      }]\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayFlexibleDimensions',\n        transform: booleanAttribute\n      }]\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayGrowAfterOpen',\n        transform: booleanAttribute\n      }]\n    }],\n    push: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayPush',\n        transform: booleanAttribute\n      }]\n    }],\n    disposeOnNavigation: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayDisposeOnNavigation',\n        transform: booleanAttribute\n      }]\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {\n  static ɵfac = function OverlayModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: OverlayModule,\n    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n    imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayRef as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_BELOW_POSITIONS as S, Overlay as a, OverlayContainer as b, OverlayConfig as c, OverlayModule as d, STANDARD_DROPDOWN_ADJACENT_POSITIONS as e, CdkConnectedOverlay as f, OverlayPositionBuilder as g, ConnectionPositionPair as h, ScrollingVisibility as i, ConnectedOverlayPositionChange as j, validateHorizontalPosition as k, ScrollStrategyOptions as l, CloseScrollStrategy as m, OverlayOutsideClickDispatcher as n, OverlayKeyboardDispatcher as o, validateVerticalPosition as v };\n", "import { b as OverlayContainer } from './overlay-module-1d184db0.mjs';\nexport { B as BlockScrollStrategy, f as CdkConnectedOverlay, C as CdkOverlayOrigin, m as CloseScrollStrategy, j as ConnectedOverlayPositionChange, h as ConnectionPositionPair, F as FlexibleConnectedPositionStrategy, G as GlobalPositionStrategy, N as NoopScrollStrategy, a as Overlay, c as OverlayConfig, b as OverlayContainer, o as OverlayKeyboardDispatcher, d as OverlayModule, n as OverlayOutsideClickDispatcher, g as OverlayPositionBuilder, O as OverlayRef, R as RepositionScrollStrategy, e as STANDARD_DROPDOWN_ADJACENT_POSITIONS, S as STANDARD_DROPDOWN_BELOW_POSITIONS, l as ScrollStrategyOptions, i as ScrollingVisibility, k as validateHorizontalPosition, v as validateVerticalPosition } from './overlay-module-1d184db0.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, RendererFactory2, Injectable } from '@angular/core';\nexport { b as CdkScrollable, S as ScrollDispatcher, V as ViewportRuler, c as ɵɵCdkFixedSizeVirtualScroll, C as ɵɵCdkScrollableModule, d as ɵɵCdkVirtualForOf, e as ɵɵCdkVirtualScrollViewport, g as ɵɵCdkVirtualScrollableElement, f as ɵɵCdkVirtualScrollableWindow } from './scrolling-module-722545e3.mjs';\nexport { D as ɵɵDir } from './bidi-module-04c03e58.mjs';\nimport '@angular/common';\nimport './platform-20fc4de8.mjs';\nimport './backwards-compatibility-08253a84.mjs';\nimport './shadow-dom-318658ae.mjs';\nimport './test-environment-f6f8bc13.mjs';\nimport './style-loader-09eecacc.mjs';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport './css-pixel-value-5d0cae55.mjs';\nimport './array-6239d2f8.mjs';\nimport './scrolling-59340c46.mjs';\nimport './portal-directives-dced6d68.mjs';\nimport './directionality-9d44e426.mjs';\nimport './id-generator-0b91c6f7.mjs';\nimport './keycodes-0e4398c6.mjs';\nimport './modifiers-3e8908bb.mjs';\nimport './element-15999318.mjs';\nimport './recycle-view-repeater-strategy-0f32b0a8.mjs';\nimport './data-source-d79c6e09.mjs';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _fullScreenEventName;\n  _cleanupFullScreenListener;\n  constructor() {\n    super();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._cleanupFullScreenListener?.();\n  }\n  _createContainer() {\n    const eventName = this._getEventName();\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    if (eventName) {\n      this._cleanupFullScreenListener?.();\n      this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n        this._adjustParentForFullscreenChange();\n      });\n    }\n  }\n  _adjustParentForFullscreenChange() {\n    if (this._containerElement) {\n      const fullscreenElement = this.getFullscreenElement();\n      const parent = fullscreenElement || this._document.body;\n      parent.appendChild(this._containerElement);\n    }\n  }\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n  static ɵfac = function FullscreenOverlayContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FullscreenOverlayContainer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FullscreenOverlayContainer,\n    factory: FullscreenOverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { FullscreenOverlayContainer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,SAAS,uBAAuB;AAC9B,QAAM,MAAM,iCAAiC;AAC/C;AAKA,SAAS,kCAAkC;AACzC,QAAM,MAAM,oCAAoC;AAClD;AAKA,SAAS,wCAAwC;AAC/C,QAAM,MAAM,6CAA6C;AAC3D;AAKA,SAAS,8BAA8B;AACrC,QAAM,MAAM,qHAA0H;AACxI;AAKA,SAAS,6BAA6B;AACpC,QAAM,MAAM,sDAAsD;AACpE;AAKA,SAAS,6BAA6B;AACpC,QAAM,MAAM,8DAA8D;AAC5E;AAMA,IAAM,SAAN,MAAa;AAAA,EACX;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,QAAQ,MAAM;AAChB,mCAA2B;AAAA,MAC7B;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,wCAAgC;AAAA,MAClC;AAAA,IACF;AACA,SAAK,gBAAgB;AACrB,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,MAAM;AAChB,WAAK,gBAAgB;AACrB,WAAK,OAAO;AAAA,IACd,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,iCAA2B;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM;AACpB,SAAK,gBAAgB;AAAA,EACvB;AACF;AAIA,IAAM,kBAAN,cAA8B,OAAO;AAAA;AAAA,EAEnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA,YAAY,WAAW,kBAAkB,UAKzC,2BAA2B,kBAAkB;AAC3C,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AAAA,EAC1B;AACF;AAIA,IAAM,iBAAN,cAA6B,OAAO;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YACA,aACA,kBACA,SACA,UAAU;AACR,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAM,UAAU,KAAK,SAAS;AACnC,SAAK,UAAU;AACf,WAAO,MAAM,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,SAAK,UAAU;AACf,WAAO,MAAM,OAAO;AAAA,EACtB;AACF;AAMA,IAAM,YAAN,cAAwB,OAAO;AAAA;AAAA,EAE7B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,UAAU,mBAAmB,aAAa,QAAQ,gBAAgB;AAAA,EACzE;AACF;AAKA,IAAM,mBAAN,MAAuB;AAAA;AAAA,EAErB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,QAAQ;AACX,6BAAqB;AAAA,MACvB;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,wCAAgC;AAAA,MAClC;AACA,UAAI,KAAK,aAAa;AACpB,8CAAsC;AAAA,MACxC;AAAA,IACF;AACA,QAAI,kBAAkB,iBAAiB;AACrC,WAAK,kBAAkB;AACvB,aAAO,KAAK,sBAAsB,MAAM;AAAA,IAC1C,WAAW,kBAAkB,gBAAgB;AAC3C,WAAK,kBAAkB;AACvB,aAAO,KAAK,qBAAqB,MAAM;AAAA,IAEzC,WAAW,KAAK,mBAAmB,kBAAkB,WAAW;AAC9D,WAAK,kBAAkB;AACvB,aAAO,KAAK,gBAAgB,MAAM;AAAA,IACpC;AACA,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,kCAA4B;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,SAAS;AACP,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,gBAAgB,IAAI;AACzC,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,aAAa,IAAI;AACf,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW;AAChB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;AAWA,IAAM,kBAAN,cAA8B,iBAAiB;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,YACA,eAKA,iCAAiC,SAAS,kBAK1C,WAAW;AACT,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,mBAAmB;AACxB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,QAAQ;AAC5B,QAAI;AAKJ,QAAI,OAAO,kBAAkB;AAC3B,YAAM,WAAW,OAAO,YAAY,OAAO,iBAAiB;AAC5D,YAAM,cAAc,SAAS,IAAI,eAAa,MAAM;AAAA,QAClD,UAAU;AAAA,MACZ,CAAC,KAAK;AACN,qBAAe,OAAO,iBAAiB,gBAAgB,OAAO,WAAW;AAAA,QACvE,OAAO,OAAO,iBAAiB;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,kBAAkB,OAAO,oBAAoB;AAAA,MAC/C,CAAC;AACD,WAAK,aAAa,MAAM,aAAa,QAAQ,CAAC;AAAA,IAChD,OAAO;AACL,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS;AACpE,cAAM,MAAM,qEAAqE;AAAA,MACnF;AACA,YAAM,SAAS,KAAK;AACpB,YAAM,kBAAkB,OAAO,YAAY,KAAK,oBAAoB,SAAS;AAC7E,YAAM,sBAAsB,gBAAgB,IAAI,qBAAqB,OAAO,QAAQ;AACpF,qBAAe,gBAAgB,OAAO,WAAW;AAAA,QAC/C;AAAA,QACA;AAAA,QACA,kBAAkB,OAAO,oBAAoB;AAAA,MAC/C,CAAC;AACD,aAAO,WAAW,aAAa,QAAQ;AACvC,WAAK,aAAa,MAAM;AAGtB,YAAI,OAAO,YAAY,GAAG;AACxB,iBAAO,WAAW,aAAa,QAAQ;AAAA,QACzC;AACA,qBAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH;AAGA,SAAK,cAAc,YAAY,KAAK,sBAAsB,YAAY,CAAC;AACvE,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,QAAI,gBAAgB,OAAO;AAC3B,QAAI,UAAU,cAAc,mBAAmB,OAAO,aAAa,OAAO,SAAS;AAAA,MACjF,UAAU,OAAO;AAAA,IACnB,CAAC;AAKD,YAAQ,UAAU,QAAQ,cAAY,KAAK,cAAc,YAAY,QAAQ,CAAC;AAI9E,YAAQ,cAAc;AACtB,SAAK,aAAa,MAAM;AACtB,UAAI,QAAQ,cAAc,QAAQ,OAAO;AACzC,UAAI,UAAU,IAAI;AAChB,sBAAc,OAAO,KAAK;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB;AAEvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAU;AAC1B,UAAM,UAAU,OAAO;AACvB,QAAI,CAAC,QAAQ,eAAe,OAAO,cAAc,eAAe,YAAY;AAC1E,YAAM,MAAM,uDAAuD;AAAA,IACrE;AAGA,UAAM,aAAa,KAAK,UAAU,cAAc,YAAY;AAC5D,YAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,SAAK,cAAc,YAAY,OAAO;AACtC,SAAK,kBAAkB;AACvB,UAAM,aAAa,MAAM;AAEvB,UAAI,WAAW,YAAY;AACzB,mBAAW,WAAW,aAAa,SAAS,UAAU;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,cAAc,OAAO;AAAA,EAC5B;AAAA;AAAA,EAEA,sBAAsB,cAAc;AAClC,WAAO,aAAa,SAAS,UAAU,CAAC;AAAA,EAC1C;AACF;AAWA,IAAM,YAAN,MAAM,mBAAkB,eAAe;AAAA,EACrC,cAAc;AACZ,UAAM,cAAc,OAAO,WAAW;AACtC,UAAM,mBAAmB,OAAO,gBAAgB;AAChD,UAAM,aAAa,gBAAgB;AAAA,EACrC;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACjC,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,0BAAN,MAAM,iCAAgC,UAAU;AAAA,EAC9C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gCAAgC,mBAAmB;AACjE,cAAQ,yCAAyC,uCAA0C,sBAAsB,wBAAuB,IAAI,qBAAqB,wBAAuB;AAAA,IAC1L;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,IACtD,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,kBAAN,MAAM,yBAAwB,iBAAiB;AAAA,EAC7C,aAAa,OAAO,eAAa;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C,iBAAiB;AAAA;AAAA,EAEjB;AAAA,EACA,cAAc;AACZ,UAAM;AAAA,EACR;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AAKjB,QAAI,KAAK,YAAY,KAAK,CAAC,UAAU,CAAC,KAAK,gBAAgB;AACzD;AAAA,IACF;AACA,QAAI,KAAK,YAAY,GAAG;AACtB,YAAM,OAAO;AAAA,IACf;AACA,QAAI,QAAQ;AACV,YAAM,OAAO,MAAM;AAAA,IACrB;AACA,SAAK,kBAAkB,UAAU;AAAA,EACnC;AAAA;AAAA,EAEA,WAAW,IAAI,aAAa;AAAA;AAAA,EAE5B,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ;AACd,SAAK,eAAe,KAAK,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,QAAQ;AAC5B,WAAO,gBAAgB,IAAI;AAG3B,UAAM,mBAAmB,OAAO,oBAAoB,OAAO,OAAO,mBAAmB,KAAK;AAC1F,UAAM,MAAM,iBAAiB,gBAAgB,OAAO,WAAW;AAAA,MAC7D,OAAO,iBAAiB;AAAA,MACxB,UAAU,OAAO,YAAY,iBAAiB;AAAA,MAC9C,kBAAkB,OAAO,oBAAoB;AAAA,MAC7C,aAAa,KAAK,cAAc;AAAA,IAClC,CAAC;AAID,QAAI,qBAAqB,KAAK,mBAAmB;AAC/C,WAAK,aAAa,EAAE,YAAY,IAAI,SAAS,UAAU,CAAC,CAAC;AAAA,IAC3D;AACA,UAAM,aAAa,MAAM,IAAI,QAAQ,CAAC;AACtC,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,GAAG;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,WAAO,gBAAgB,IAAI;AAC3B,UAAM,UAAU,KAAK,kBAAkB,mBAAmB,OAAO,aAAa,OAAO,SAAS;AAAA,MAC5F,UAAU,OAAO;AAAA,IACnB,CAAC;AACD,UAAM,aAAa,MAAM,KAAK,kBAAkB,MAAM,CAAC;AACvD,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,OAAO;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAU;AAC1B,UAAM,UAAU,OAAO;AACvB,QAAI,CAAC,QAAQ,eAAe,OAAO,cAAc,eAAe,YAAY;AAC1E,YAAM,MAAM,uDAAuD;AAAA,IACrE;AAGA,UAAM,aAAa,KAAK,UAAU,cAAc,YAAY;AAC5D,WAAO,gBAAgB,IAAI;AAC3B,YAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,SAAK,aAAa,EAAE,YAAY,OAAO;AACvC,SAAK,kBAAkB;AACvB,UAAM,aAAa,MAAM;AACvB,UAAI,WAAW,YAAY;AACzB,mBAAW,WAAW,aAAa,SAAS,UAAU;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe;AACb,UAAM,gBAAgB,KAAK,kBAAkB,QAAQ;AAGrD,WAAO,cAAc,aAAa,cAAc,eAAe,gBAAgB,cAAc;AAAA,EAC/F;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,IACzC;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,sBAAN,MAAM,6BAA4B,gBAAgB;AAAA,EAChD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAC7D,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,iBAAiB,QAAQ;AAAA,IACvC;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,IAClF,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,EACpF,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,MAClF,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,IACpF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACpqBH,IAAM,0BAA0B,uBAAuB;AAIvD,IAAM,sBAAN,MAA0B;AAAA,EACxB;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,MAAM;AAAA,EACR;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,gBAAgB,UAAU;AACpC,SAAK,iBAAiB;AACtB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,SAAS;AAAA,EAAC;AAAA;AAAA,EAEV,SAAS;AACP,QAAI,KAAK,cAAc,GAAG;AACxB,YAAM,OAAO,KAAK,UAAU;AAC5B,WAAK,0BAA0B,KAAK,eAAe,0BAA0B;AAE7E,WAAK,oBAAoB,OAAO,KAAK,MAAM,QAAQ;AACnD,WAAK,oBAAoB,MAAM,KAAK,MAAM,OAAO;AAGjD,WAAK,MAAM,OAAO,oBAAoB,CAAC,KAAK,wBAAwB,IAAI;AACxE,WAAK,MAAM,MAAM,oBAAoB,CAAC,KAAK,wBAAwB,GAAG;AACtE,WAAK,UAAU,IAAI,wBAAwB;AAC3C,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,YAAY;AACnB,YAAM,OAAO,KAAK,UAAU;AAC5B,YAAM,OAAO,KAAK,UAAU;AAC5B,YAAM,YAAY,KAAK;AACvB,YAAM,YAAY,KAAK;AACvB,YAAM,6BAA6B,UAAU,kBAAkB;AAC/D,YAAM,6BAA6B,UAAU,kBAAkB;AAC/D,WAAK,aAAa;AAClB,gBAAU,OAAO,KAAK,oBAAoB;AAC1C,gBAAU,MAAM,KAAK,oBAAoB;AACzC,WAAK,UAAU,OAAO,wBAAwB;AAM9C,UAAI,yBAAyB;AAC3B,kBAAU,iBAAiB,UAAU,iBAAiB;AAAA,MACxD;AACA,aAAO,OAAO,KAAK,wBAAwB,MAAM,KAAK,wBAAwB,GAAG;AACjF,UAAI,yBAAyB;AAC3B,kBAAU,iBAAiB;AAC3B,kBAAU,iBAAiB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AAId,UAAM,OAAO,KAAK,UAAU;AAC5B,QAAI,KAAK,UAAU,SAAS,wBAAwB,KAAK,KAAK,YAAY;AACxE,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,UAAU;AAC5B,UAAM,WAAW,KAAK,eAAe,gBAAgB;AACrD,WAAO,KAAK,eAAe,SAAS,UAAU,KAAK,cAAc,SAAS;AAAA,EAC5E;AACF;AAKA,SAAS,2CAA2C;AAClD,SAAO,MAAM,4CAA4C;AAC3D;AAKA,IAAM,sBAAN,MAA0B;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,YAAY,mBAAmB,SAAS,gBAAgB,SAAS;AAC/D,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,QAAI,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AACvE,YAAM,yCAAyC;AAAA,IACjD;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,qBAAqB;AAC5B;AAAA,IACF;AACA,UAAM,SAAS,KAAK,kBAAkB,SAAS,CAAC,EAAE,KAAK,OAAO,gBAAc;AAC1E,aAAO,CAAC,cAAc,CAAC,KAAK,YAAY,eAAe,SAAS,WAAW,cAAc,EAAE,aAAa;AAAA,IAC1G,CAAC,CAAC;AACF,QAAI,KAAK,WAAW,KAAK,QAAQ,aAAa,KAAK,QAAQ,YAAY,GAAG;AACxE,WAAK,yBAAyB,KAAK,eAAe,0BAA0B,EAAE;AAC9E,WAAK,sBAAsB,OAAO,UAAU,MAAM;AAChD,cAAM,iBAAiB,KAAK,eAAe,0BAA0B,EAAE;AACvE,YAAI,KAAK,IAAI,iBAAiB,KAAK,sBAAsB,IAAI,KAAK,QAAQ,WAAW;AACnF,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,YAAY,eAAe;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,sBAAsB,OAAO,UAAU,KAAK,OAAO;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,UAAU,MAAM;AACd,SAAK,QAAQ;AACb,QAAI,KAAK,YAAY,YAAY,GAAG;AAClC,WAAK,QAAQ,IAAI,MAAM,KAAK,YAAY,OAAO,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AAGA,IAAM,qBAAN,MAAyB;AAAA;AAAA,EAEvB,SAAS;AAAA,EAAC;AAAA;AAAA,EAEV,UAAU;AAAA,EAAC;AAAA;AAAA,EAEX,SAAS;AAAA,EAAC;AACZ;AASA,SAAS,6BAA6B,SAAS,kBAAkB;AAC/D,SAAO,iBAAiB,KAAK,qBAAmB;AAC9C,UAAM,eAAe,QAAQ,SAAS,gBAAgB;AACtD,UAAM,eAAe,QAAQ,MAAM,gBAAgB;AACnD,UAAM,cAAc,QAAQ,QAAQ,gBAAgB;AACpD,UAAM,eAAe,QAAQ,OAAO,gBAAgB;AACpD,WAAO,gBAAgB,gBAAgB,eAAe;AAAA,EACxD,CAAC;AACH;AAQA,SAAS,4BAA4B,SAAS,kBAAkB;AAC9D,SAAO,iBAAiB,KAAK,yBAAuB;AAClD,UAAM,eAAe,QAAQ,MAAM,oBAAoB;AACvD,UAAM,eAAe,QAAQ,SAAS,oBAAoB;AAC1D,UAAM,cAAc,QAAQ,OAAO,oBAAoB;AACvD,UAAM,eAAe,QAAQ,QAAQ,oBAAoB;AACzD,WAAO,gBAAgB,gBAAgB,eAAe;AAAA,EACxD,CAAC;AACH;AAKA,IAAM,2BAAN,MAA+B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB;AAAA,EACA,YAAY,mBAAmB,gBAAgB,SAAS,SAAS;AAC/D,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,QAAI,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AACvE,YAAM,yCAAyC;AAAA,IACjD;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,WAAW,KAAK,UAAU,KAAK,QAAQ,iBAAiB;AAC9D,WAAK,sBAAsB,KAAK,kBAAkB,SAAS,QAAQ,EAAE,UAAU,MAAM;AACnF,aAAK,YAAY,eAAe;AAEhC,YAAI,KAAK,WAAW,KAAK,QAAQ,WAAW;AAC1C,gBAAM,cAAc,KAAK,YAAY,eAAe,sBAAsB;AAC1E,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK,eAAe,gBAAgB;AAGxC,gBAAM,cAAc,CAAC;AAAA,YACnB;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,UACR,CAAC;AACD,cAAI,6BAA6B,aAAa,WAAW,GAAG;AAC1D,iBAAK,QAAQ;AACb,iBAAK,QAAQ,IAAI,MAAM,KAAK,YAAY,OAAO,CAAC;AAAA,UAClD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AACF;AAQA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,iBAAiB,OAAO,aAAa;AAAA,EACrC,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,OAAO,MAAM,IAAI,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC,QAAQ,YAAU,IAAI,oBAAoB,KAAK,mBAAmB,KAAK,SAAS,KAAK,gBAAgB,MAAM;AAAA;AAAA,EAE3G,QAAQ,MAAM,IAAI,oBAAoB,KAAK,gBAAgB,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzE,aAAa,YAAU,IAAI,yBAAyB,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,SAAS,MAAM;AAAA,EACrH,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,gBAAN,MAAoB;AAAA;AAAA,EAElB;AAAA;AAAA,EAEA,iBAAiB,IAAI,mBAAmB;AAAA;AAAA,EAExC,aAAa;AAAA;AAAA,EAEb,cAAc;AAAA;AAAA,EAEd,gBAAgB;AAAA;AAAA,EAEhB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AAAA,EACtB,YAAY,QAAQ;AAClB,QAAI,QAAQ;AAIV,YAAM,aAAa,OAAO,KAAK,MAAM;AACrC,iBAAW,OAAO,YAAY;AAC5B,YAAI,OAAO,GAAG,MAAM,QAAW;AAO7B,eAAK,GAAG,IAAI,OAAO,GAAG;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA4DA,IAAM,iCAAN,MAAqC;AAAA,EACnC;AAAA,EACA;AAAA,EACA,YACA,gBACA,0BAA0B;AACxB,SAAK,iBAAiB;AACtB,SAAK,2BAA2B;AAAA,EAClC;AACF;AAOA,SAAS,yBAAyB,UAAU,OAAO;AACjD,MAAI,UAAU,SAAS,UAAU,YAAY,UAAU,UAAU;AAC/D,UAAM,MAAM,8BAA8B,QAAQ,KAAK,KAAK,0CAA+C;AAAA,EAC7G;AACF;AAOA,SAAS,2BAA2B,UAAU,OAAO;AACnD,MAAI,UAAU,WAAW,UAAU,SAAS,UAAU,UAAU;AAC9D,UAAM,MAAM,8BAA8B,QAAQ,KAAK,KAAK,yCAA8C;AAAA,EAC5G;AACF;AAOA,IAAM,wBAAN,MAAM,uBAAsB;AAAA;AAAA,EAE1B,oBAAoB,CAAC;AAAA,EACrB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,YAAY;AAEd,SAAK,OAAO,UAAU;AACtB,SAAK,kBAAkB,KAAK,UAAU;AAAA,EACxC;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,UAAM,QAAQ,KAAK,kBAAkB,QAAQ,UAAU;AACvD,QAAI,QAAQ,IAAI;AACd,WAAK,kBAAkB,OAAO,OAAO,CAAC;AAAA,IACxC;AAEA,QAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,4BAAN,MAAM,mCAAkC,sBAAsB;AAAA,EAC5D,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,UAAM,IAAI,UAAU;AAEpB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,kBAAkB,KAAK,UAAU,OAAO,QAAQ,WAAW,KAAK,gBAAgB;AAAA,MACvF,CAAC;AACD,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,aAAa;AACpB,WAAK,kBAAkB;AACvB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,WAAS;AAC1B,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,SAAS,SAAS,GAAG,IAAI,IAAI,KAAK;AAO7C,UAAI,SAAS,CAAC,EAAE,eAAe,UAAU,SAAS,GAAG;AACnD,aAAK,QAAQ,IAAI,MAAM,SAAS,CAAC,EAAE,eAAe,KAAK,KAAK,CAAC;AAC7D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kCAAkC,mBAAmB;AACnE,cAAQ,2CAA2C,yCAA4C,sBAAsB,0BAAyB,IAAI,qBAAqB,0BAAyB;AAAA,IAClM;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,IACnC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,gCAAN,MAAM,uCAAsC,sBAAsB;AAAA,EAChE,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D;AAAA,EACA,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,UAAM,IAAI,UAAU;AAOpB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,OAAO,KAAK,UAAU;AAC5B,YAAM,eAAe;AAAA,QACnB,SAAS;AAAA,MACX;AACA,WAAK,YAAY,KAAK,QAAQ,kBAAkB,MAAM,CAAC,sBAAsB,KAAK,WAAW,MAAM,eAAe,KAAK,sBAAsB,YAAY,GAAG,sBAAsB,KAAK,WAAW,MAAM,SAAS,KAAK,gBAAgB,YAAY,GAAG,sBAAsB,KAAK,WAAW,MAAM,YAAY,KAAK,gBAAgB,YAAY,GAAG,sBAAsB,KAAK,WAAW,MAAM,eAAe,KAAK,gBAAgB,YAAY,CAAC,CAAC;AAG/a,UAAI,KAAK,UAAU,OAAO,CAAC,KAAK,mBAAmB;AACjD,aAAK,uBAAuB,KAAK,MAAM;AACvC,aAAK,MAAM,SAAS;AACpB,aAAK,oBAAoB;AAAA,MAC3B;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,QAAQ,aAAW,QAAQ,CAAC;AAC5C,WAAK,YAAY;AACjB,UAAI,KAAK,UAAU,OAAO,KAAK,mBAAmB;AAChD,aAAK,UAAU,KAAK,MAAM,SAAS,KAAK;AACxC,aAAK,oBAAoB;AAAA,MAC3B;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB,WAAS;AAC9B,SAAK,0BAA0B,gBAAgB,KAAK;AAAA,EACtD;AAAA;AAAA,EAEA,iBAAiB,WAAS;AACxB,UAAM,SAAS,gBAAgB,KAAK;AAOpC,UAAM,SAAS,MAAM,SAAS,WAAW,KAAK,0BAA0B,KAAK,0BAA0B;AAGvG,SAAK,0BAA0B;AAI/B,UAAM,WAAW,KAAK,kBAAkB,MAAM;AAK9C,aAAS,IAAI,SAAS,SAAS,GAAG,IAAI,IAAI,KAAK;AAC7C,YAAM,aAAa,SAAS,CAAC;AAC7B,UAAI,WAAW,sBAAsB,UAAU,SAAS,KAAK,CAAC,WAAW,YAAY,GAAG;AACtF;AAAA,MACF;AAIA,UAAI,wBAAwB,WAAW,gBAAgB,MAAM,KAAK,wBAAwB,WAAW,gBAAgB,MAAM,GAAG;AAC5H;AAAA,MACF;AACA,YAAM,uBAAuB,WAAW;AAExC,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,IAAI,MAAM,qBAAqB,KAAK,KAAK,CAAC;AAAA,MACzD,OAAO;AACL,6BAAqB,KAAK,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sCAAsC,mBAAmB;AACvE,cAAQ,+CAA+C,6CAAgD,sBAAsB,8BAA6B,IAAI,qBAAqB,8BAA6B;AAAA,IAClN;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,+BAA8B;AAAA,IACvC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,SAAS,wBAAwB,QAAQ,OAAO;AAC9C,QAAM,qBAAqB,OAAO,eAAe,eAAe;AAChE,MAAI,UAAU;AACd,SAAO,SAAS;AACd,QAAI,YAAY,QAAQ;AACtB,aAAO;AAAA,IACT;AACA,cAAU,sBAAsB,mBAAmB,aAAa,QAAQ,OAAO,QAAQ;AAAA,EACzF;AACA,SAAO;AACT;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,4BAA4B,EAAE;AAAA,IAC1C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC7D,QAAQ,CAAC,miDAAmiD;AAAA,IAC5iD,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,4BAA4B;AAAA,MAC9B;AAAA,MACA,QAAQ,CAAC,miDAAmiD;AAAA,IAC9iD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,eAAe,OAAO,sBAAsB;AAAA,EAC5C,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,mBAAmB,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AACpB,SAAK,YAAY;AACjB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,iBAAiB;AAIvB,QAAI,KAAK,UAAU,aAAa,mBAAmB,GAAG;AACpD,YAAM,6BAA6B,KAAK,UAAU,iBAAiB,IAAI,cAAc,yBAA8B,cAAc,mBAAmB;AAGpJ,eAAS,IAAI,GAAG,IAAI,2BAA2B,QAAQ,KAAK;AAC1D,mCAA2B,CAAC,EAAE,OAAO;AAAA,MACvC;AAAA,IACF;AACA,UAAM,YAAY,KAAK,UAAU,cAAc,KAAK;AACpD,cAAU,UAAU,IAAI,cAAc;AAUtC,QAAI,mBAAmB,GAAG;AACxB,gBAAU,aAAa,YAAY,MAAM;AAAA,IAC3C,WAAW,CAAC,KAAK,UAAU,WAAW;AACpC,gBAAU,aAAa,YAAY,QAAQ;AAAA,IAC7C;AACA,SAAK,UAAU,KAAK,YAAY,SAAS;AACzC,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,aAAa,KAAK,sBAAsB;AAAA,EAC/C;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,cAAN,MAAkB;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,WAAW,SAAS,SAAS;AACjD,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,UAAU,IAAI,sBAAsB;AACjD,SAAK,gBAAgB,UAAU,OAAO,KAAK,SAAS,SAAS,OAAO;AAAA,EACtE;AAAA,EACA,SAAS;AACP,SAAK,QAAQ,kBAAkB,MAAM;AACnC,YAAM,UAAU,KAAK;AACrB,mBAAa,KAAK,gBAAgB;AAClC,WAAK,wBAAwB;AAC7B,WAAK,wBAAwB,KAAK,UAAU,OAAO,SAAS,iBAAiB,KAAK,OAAO;AACzF,WAAK,mBAAmB,WAAW,KAAK,SAAS,GAAG;AAGpD,cAAQ,MAAM,gBAAgB;AAC9B,cAAQ,UAAU,OAAO,8BAA8B;AAAA,IACzD,CAAC;AAAA,EACH;AAAA,EACA,UAAU,MAAM;AACd,iBAAa,KAAK,gBAAgB;AAClC,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,gBAAgB,KAAK,wBAAwB,KAAK,mBAAmB;AAC1E,SAAK,QAAQ,OAAO;AAAA,EACtB;AACF;AAMA,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB,IAAI,QAAQ;AAAA,EAC7B,eAAe,IAAI,QAAQ;AAAA,EAC3B,eAAe,IAAI,QAAQ;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,mBAAmB,aAAa;AAAA,EAChC,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA,EAEA,iBAAiB,IAAI,QAAQ;AAAA;AAAA,EAE7B,wBAAwB,IAAI,QAAQ;AAAA,EACpC,WAAW,IAAI,QAAQ;AAAA,EACvB;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,eAAe,OAAO,OAAO,SAAS,SAAS,qBAAqB,WAAW,WAAW,yBAAyB,sBAAsB,OAAO,WAAW,WAAW;AAChL,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,0BAA0B;AAC/B,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,QAAQ,gBAAgB;AAC1B,WAAK,kBAAkB,QAAQ;AAC/B,WAAK,gBAAgB,OAAO,IAAI;AAAA,IAClC;AACA,SAAK,oBAAoB,QAAQ;AAIjC,SAAK,kBAAkB,UAAU,MAAM,YAAY,MAAM;AACvD,WAAK,SAAS,KAAK;AAAA,IACrB,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK,cAAc,WAAW;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ;AAGb,QAAI,CAAC,KAAK,MAAM,iBAAiB,KAAK,qBAAqB;AACzD,WAAK,oBAAoB,YAAY,KAAK,KAAK;AAAA,IACjD;AACA,UAAM,eAAe,KAAK,cAAc,OAAO,MAAM;AACrD,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,OAAO,IAAI;AAAA,IACpC;AACA,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,wBAAwB;AAC7B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,OAAO;AAAA,IAC9B;AAIA,SAAK,qBAAqB,QAAQ;AAGlC,SAAK,sBAAsB,gBAAgB,MAAM;AAE/C,UAAI,KAAK,YAAY,GAAG;AACtB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAED,SAAK,qBAAqB,IAAI;AAC9B,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,eAAe,KAAK,OAAO,KAAK,QAAQ,YAAY,IAAI;AAAA,IAC/D;AAEA,SAAK,aAAa,KAAK;AAEvB,SAAK,oBAAoB,IAAI,IAAI;AACjC,QAAI,KAAK,QAAQ,qBAAqB;AACpC,WAAK,mBAAmB,KAAK,UAAU,UAAU,MAAM,KAAK,QAAQ,CAAC;AAAA,IACvE;AACA,SAAK,wBAAwB,IAAI,IAAI;AAIrC,QAAI,OAAO,cAAc,cAAc,YAAY;AAMjD,mBAAa,UAAU,MAAM;AAC3B,YAAI,KAAK,YAAY,GAAG;AAItB,eAAK,QAAQ,kBAAkB,MAAM,QAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,OAAO,CAAC,CAAC;AAAA,QAClF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,SAAK,eAAe;AAIpB,SAAK,qBAAqB,KAAK;AAC/B,QAAI,KAAK,qBAAqB,KAAK,kBAAkB,QAAQ;AAC3D,WAAK,kBAAkB,OAAO;AAAA,IAChC;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AACA,UAAM,mBAAmB,KAAK,cAAc,OAAO;AAEnD,SAAK,aAAa,KAAK;AAEvB,SAAK,oBAAoB,OAAO,IAAI;AAGpC,SAAK,wBAAwB;AAC7B,SAAK,iBAAiB,YAAY;AAClC,SAAK,wBAAwB,OAAO,IAAI;AACxC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AACA,SAAK,uBAAuB;AAC5B,SAAK,cAAc,QAAQ;AAC3B,SAAK,iBAAiB,YAAY;AAClC,SAAK,oBAAoB,OAAO,IAAI;AACpC,SAAK,cAAc,QAAQ;AAC3B,SAAK,aAAa,SAAS;AAC3B,SAAK,eAAe,SAAS;AAC7B,SAAK,eAAe,SAAS;AAC7B,SAAK,sBAAsB,SAAS;AACpC,SAAK,wBAAwB,OAAO,IAAI;AACxC,SAAK,OAAO,OAAO;AACnB,SAAK,qBAAqB,QAAQ;AAClC,SAAK,sBAAsB,KAAK,QAAQ,KAAK,QAAQ,KAAK,eAAe;AACzE,QAAI,YAAY;AACd,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,SAAK,aAAa,SAAS;AAC3B,SAAK,gBAAgB,QAAQ;AAC7B,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,cAAc,YAAY;AAAA,EACxC;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB,UAAU;AAC/B,QAAI,aAAa,KAAK,mBAAmB;AACvC;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AACA,SAAK,oBAAoB;AACzB,QAAI,KAAK,YAAY,GAAG;AACtB,eAAS,OAAO,IAAI;AACpB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,YAAY;AACrB,SAAK,UAAU,kCACV,KAAK,UACL;AAEL,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa,KAAK;AAChB,SAAK,UAAU,iCACV,KAAK,UADK;AAAA,MAEb,WAAW;AAAA,IACb;AACA,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA,EAEA,cAAc,SAAS;AACrB,QAAI,KAAK,OAAO;AACd,WAAK,eAAe,KAAK,OAAO,SAAS,IAAI;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,OAAO;AACd,WAAK,eAAe,KAAK,OAAO,SAAS,KAAK;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,UAAM,YAAY,KAAK,QAAQ;AAC/B,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,WAAO,OAAO,cAAc,WAAW,YAAY,UAAU;AAAA,EAC/D;AAAA;AAAA,EAEA,qBAAqB,UAAU;AAC7B,QAAI,aAAa,KAAK,iBAAiB;AACrC;AAAA,IACF;AACA,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB;AACvB,QAAI,KAAK,YAAY,GAAG;AACtB,eAAS,OAAO,IAAI;AACpB,eAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,MAAM,aAAa,OAAO,KAAK,aAAa,CAAC;AAAA,EACpD;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,QAAQ,oBAAoB,KAAK,QAAQ,KAAK;AACpD,UAAM,SAAS,oBAAoB,KAAK,QAAQ,MAAM;AACtD,UAAM,WAAW,oBAAoB,KAAK,QAAQ,QAAQ;AAC1D,UAAM,YAAY,oBAAoB,KAAK,QAAQ,SAAS;AAC5D,UAAM,WAAW,oBAAoB,KAAK,QAAQ,QAAQ;AAC1D,UAAM,YAAY,oBAAoB,KAAK,QAAQ,SAAS;AAAA,EAC9D;AAAA;AAAA,EAEA,qBAAqB,eAAe;AAClC,SAAK,MAAM,MAAM,gBAAgB,gBAAgB,KAAK;AAAA,EACxD;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,eAAe;AACrB,SAAK,cAAc,QAAQ;AAC3B,SAAK,eAAe,IAAI,YAAY,KAAK,WAAW,KAAK,WAAW,KAAK,SAAS,WAAS;AACzF,WAAK,eAAe,KAAK,KAAK;AAAA,IAChC,CAAC;AACD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,aAAa,QAAQ,UAAU,IAAI,qCAAqC;AAAA,IAC/E;AACA,QAAI,KAAK,QAAQ,eAAe;AAC9B,WAAK,eAAe,KAAK,aAAa,SAAS,KAAK,QAAQ,eAAe,IAAI;AAAA,IACjF;AAGA,SAAK,MAAM,cAAc,aAAa,KAAK,aAAa,SAAS,KAAK,KAAK;AAE3E,QAAI,CAAC,KAAK,uBAAuB,OAAO,0BAA0B,aAAa;AAC7E,WAAK,QAAQ,kBAAkB,MAAM;AACnC,8BAAsB,MAAM,KAAK,cAAc,QAAQ,UAAU,IAAI,YAAY,CAAC;AAAA,MACpF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,aAAa,QAAQ,UAAU,IAAI,YAAY;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB;AACrB,QAAI,KAAK,MAAM,aAAa;AAC1B,WAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,qBAAqB;AAC5B,WAAK,cAAc,QAAQ;AAC3B,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,cAAc,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS,YAAY,OAAO;AACzC,UAAM,UAAU,YAAY,cAAc,CAAC,CAAC,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AAC7D,QAAI,QAAQ,QAAQ;AAClB,cAAQ,QAAQ,UAAU,IAAI,GAAG,OAAO,IAAI,QAAQ,UAAU,OAAO,GAAG,OAAO;AAAA,IACjF;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B;AAIxB,SAAK,QAAQ,kBAAkB,MAAM;AAInC,YAAM,eAAe,KAAK,SAAS,KAAK,UAAU,MAAM,KAAK,cAAc,KAAK,YAAY,CAAC,CAAC,EAAE,UAAU,MAAM;AAG9G,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,SAAS,KAAK,MAAM,SAAS,WAAW,GAAG;AAClE,cAAI,KAAK,SAAS,KAAK,QAAQ,YAAY;AACzC,iBAAK,eAAe,KAAK,OAAO,KAAK,QAAQ,YAAY,KAAK;AAAA,UAChE;AACA,cAAI,KAAK,SAAS,KAAK,MAAM,eAAe;AAC1C,iBAAK,sBAAsB,KAAK,MAAM;AACtC,iBAAK,MAAM,OAAO;AAAA,UACpB;AACA,uBAAa,YAAY;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,iBAAiB,KAAK;AAC5B,oBAAgB,QAAQ;AACxB,oBAAgB,SAAS;AAAA,EAC3B;AACF;AAKA,IAAM,mBAAmB;AAEzB,IAAM,iBAAiB;AAQvB,IAAM,oCAAN,MAAwC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,uBAAuB;AAAA,IACrB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA;AAAA,EAEX,iBAAiB;AAAA;AAAA,EAEjB,yBAAyB;AAAA;AAAA,EAEzB,kBAAkB;AAAA;AAAA,EAElB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,eAAe,CAAC;AAAA;AAAA,EAEhB,sBAAsB,CAAC;AAAA;AAAA,EAEvB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,mBAAmB,IAAI,QAAQ;AAAA;AAAA,EAE/B,sBAAsB,aAAa;AAAA;AAAA,EAEnC,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA;AAAA,EAEX;AAAA;AAAA,EAEA,uBAAuB,CAAC;AAAA;AAAA,EAExB;AAAA;AAAA,EAEA,kBAAkB,KAAK;AAAA;AAAA,EAEvB,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,aAAa,gBAAgB,WAAW,WAAW,mBAAmB;AAChF,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,QAAI,KAAK,eAAe,eAAe,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAC1G,YAAM,MAAM,0DAA0D;AAAA,IACxE;AACA,SAAK,mBAAmB;AACxB,eAAW,YAAY,UAAU,IAAI,gBAAgB;AACrD,SAAK,cAAc;AACnB,SAAK,eAAe,WAAW;AAC/B,SAAK,QAAQ,WAAW;AACxB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB,YAAY;AACrC,SAAK,sBAAsB,KAAK,eAAe,OAAO,EAAE,UAAU,MAAM;AAItE,WAAK,mBAAmB;AACxB,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,QAAQ;AAEN,QAAI,KAAK,eAAe,CAAC,KAAK,UAAU,WAAW;AACjD;AAAA,IACF;AAIA,QAAI,CAAC,KAAK,oBAAoB,KAAK,mBAAmB,KAAK,eAAe;AACxE,WAAK,oBAAoB;AACzB;AAAA,IACF;AACA,SAAK,mBAAmB;AACxB,SAAK,2BAA2B;AAChC,SAAK,wBAAwB;AAI7B,SAAK,gBAAgB,KAAK,yBAAyB;AACnD,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,eAAe,KAAK,MAAM,sBAAsB;AACrD,SAAK,iBAAiB,KAAK,kBAAkB,oBAAoB,EAAE,sBAAsB;AACzF,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK;AACzB,UAAM,eAAe,KAAK;AAC1B,UAAM,gBAAgB,KAAK;AAE3B,UAAM,eAAe,CAAC;AAEtB,QAAI;AAGJ,aAAS,OAAO,KAAK,qBAAqB;AAExC,UAAI,cAAc,KAAK,gBAAgB,YAAY,eAAe,GAAG;AAIrE,UAAI,eAAe,KAAK,iBAAiB,aAAa,aAAa,GAAG;AAEtE,UAAI,aAAa,KAAK,eAAe,cAAc,aAAa,cAAc,GAAG;AAEjF,UAAI,WAAW,4BAA4B;AACzC,aAAK,YAAY;AACjB,aAAK,eAAe,KAAK,WAAW;AACpC;AAAA,MACF;AAGA,UAAI,KAAK,8BAA8B,YAAY,cAAc,YAAY,GAAG;AAG9E,qBAAa,KAAK;AAAA,UAChB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR;AAAA,UACA,iBAAiB,KAAK,0BAA0B,aAAa,GAAG;AAAA,QAClE,CAAC;AACD;AAAA,MACF;AAIA,UAAI,CAAC,YAAY,SAAS,WAAW,cAAc,WAAW,aAAa;AACzE,mBAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,aAAa,QAAQ;AACvB,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,iBAAW,OAAO,cAAc;AAC9B,cAAM,QAAQ,IAAI,gBAAgB,QAAQ,IAAI,gBAAgB,UAAU,IAAI,SAAS,UAAU;AAC/F,YAAI,QAAQ,WAAW;AACrB,sBAAY;AACZ,oBAAU;AAAA,QACZ;AAAA,MACF;AACA,WAAK,YAAY;AACjB,WAAK,eAAe,QAAQ,UAAU,QAAQ,MAAM;AACpD;AAAA,IACF;AAGA,QAAI,KAAK,UAAU;AAEjB,WAAK,YAAY;AACjB,WAAK,eAAe,SAAS,UAAU,SAAS,WAAW;AAC3D;AAAA,IACF;AAGA,SAAK,eAAe,SAAS,UAAU,SAAS,WAAW;AAAA,EAC7D;AAAA,EACA,SAAS;AACP,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB,YAAY;AAAA,EACvC;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AAGA,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,aAAa,OAAO;AAAA,QACpC,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,OAAO;AACd,WAAK,2BAA2B;AAAA,IAClC;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,YAAY,UAAU,OAAO,gBAAgB;AAAA,IAChE;AACA,SAAK,OAAO;AACZ,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,QAAI,KAAK,eAAe,CAAC,KAAK,UAAU,WAAW;AACjD;AAAA,IACF;AACA,UAAM,eAAe,KAAK;AAC1B,QAAI,cAAc;AAChB,WAAK,cAAc,KAAK,eAAe;AACvC,WAAK,eAAe,KAAK,MAAM,sBAAsB;AACrD,WAAK,gBAAgB,KAAK,yBAAyB;AACnD,WAAK,iBAAiB,KAAK,kBAAkB,oBAAoB,EAAE,sBAAsB;AACzF,YAAM,cAAc,KAAK,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,YAAY;AAC5F,WAAK,eAAe,cAAc,WAAW;AAAA,IAC/C,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,aAAa;AACpC,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,WAAW;AACvB,SAAK,sBAAsB;AAG3B,QAAI,UAAU,QAAQ,KAAK,aAAa,MAAM,IAAI;AAChD,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB,qBAAqB,MAAM;AAChD,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,gBAAgB,MAAM;AACtC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,UAAU,MAAM;AACvB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,WAAW,MAAM;AAClC,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,UAAU;AAC9B,SAAK,2BAA2B;AAChC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,YAAY,eAAe,KAAK;AAC9C,QAAI;AACJ,QAAI,IAAI,WAAW,UAAU;AAG3B,UAAI,WAAW,OAAO,WAAW,QAAQ;AAAA,IAC3C,OAAO;AACL,YAAM,SAAS,KAAK,OAAO,IAAI,WAAW,QAAQ,WAAW;AAC7D,YAAM,OAAO,KAAK,OAAO,IAAI,WAAW,OAAO,WAAW;AAC1D,UAAI,IAAI,WAAW,UAAU,SAAS;AAAA,IACxC;AAGA,QAAI,cAAc,OAAO,GAAG;AAC1B,WAAK,cAAc;AAAA,IACrB;AACA,QAAI;AACJ,QAAI,IAAI,WAAW,UAAU;AAC3B,UAAI,WAAW,MAAM,WAAW,SAAS;AAAA,IAC3C,OAAO;AACL,UAAI,IAAI,WAAW,QAAQ,WAAW,MAAM,WAAW;AAAA,IACzD;AAMA,QAAI,cAAc,MAAM,GAAG;AACzB,WAAK,cAAc;AAAA,IACrB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,aAAa,aAAa,KAAK;AAG9C,QAAI;AACJ,QAAI,IAAI,YAAY,UAAU;AAC5B,sBAAgB,CAAC,YAAY,QAAQ;AAAA,IACvC,WAAW,IAAI,aAAa,SAAS;AACnC,sBAAgB,KAAK,OAAO,IAAI,CAAC,YAAY,QAAQ;AAAA,IACvD,OAAO;AACL,sBAAgB,KAAK,OAAO,IAAI,IAAI,CAAC,YAAY;AAAA,IACnD;AACA,QAAI;AACJ,QAAI,IAAI,YAAY,UAAU;AAC5B,sBAAgB,CAAC,YAAY,SAAS;AAAA,IACxC,OAAO;AACL,sBAAgB,IAAI,YAAY,QAAQ,IAAI,CAAC,YAAY;AAAA,IAC3D;AAEA,WAAO;AAAA,MACL,GAAG,YAAY,IAAI;AAAA,MACnB,GAAG,YAAY,IAAI;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO,gBAAgB,UAAU,UAAU;AAGxD,UAAM,UAAU,6BAA6B,cAAc;AAC3D,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAC3C,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAE3C,QAAI,SAAS;AACX,WAAK;AAAA,IACP;AACA,QAAI,SAAS;AACX,WAAK;AAAA,IACP;AAEA,QAAI,eAAe,IAAI;AACvB,QAAI,gBAAgB,IAAI,QAAQ,QAAQ,SAAS;AACjD,QAAI,cAAc,IAAI;AACtB,QAAI,iBAAiB,IAAI,QAAQ,SAAS,SAAS;AAEnD,QAAI,eAAe,KAAK,mBAAmB,QAAQ,OAAO,cAAc,aAAa;AACrF,QAAI,gBAAgB,KAAK,mBAAmB,QAAQ,QAAQ,aAAa,cAAc;AACvF,QAAI,cAAc,eAAe;AACjC,WAAO;AAAA,MACL;AAAA,MACA,4BAA4B,QAAQ,QAAQ,QAAQ,WAAW;AAAA,MAC/D,0BAA0B,kBAAkB,QAAQ;AAAA,MACpD,4BAA4B,gBAAgB,QAAQ;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,KAAK,OAAO,UAAU;AAClD,QAAI,KAAK,wBAAwB;AAC/B,YAAM,kBAAkB,SAAS,SAAS,MAAM;AAChD,YAAM,iBAAiB,SAAS,QAAQ,MAAM;AAC9C,YAAM,YAAY,cAAc,KAAK,YAAY,UAAU,EAAE,SAAS;AACtE,YAAM,WAAW,cAAc,KAAK,YAAY,UAAU,EAAE,QAAQ;AACpE,YAAM,cAAc,IAAI,4BAA4B,aAAa,QAAQ,aAAa;AACtF,YAAM,gBAAgB,IAAI,8BAA8B,YAAY,QAAQ,YAAY;AACxF,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,qBAAqB,OAAO,gBAAgB,gBAAgB;AAI1D,QAAI,KAAK,uBAAuB,KAAK,iBAAiB;AACpD,aAAO;AAAA,QACL,GAAG,MAAM,IAAI,KAAK,oBAAoB;AAAA,QACtC,GAAG,MAAM,IAAI,KAAK,oBAAoB;AAAA,MACxC;AAAA,IACF;AAGA,UAAM,UAAU,6BAA6B,cAAc;AAC3D,UAAM,WAAW,KAAK;AAGtB,UAAM,gBAAgB,KAAK,IAAI,MAAM,IAAI,QAAQ,QAAQ,SAAS,OAAO,CAAC;AAC1E,UAAM,iBAAiB,KAAK,IAAI,MAAM,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC;AAC7E,UAAM,cAAc,KAAK,IAAI,SAAS,MAAM,eAAe,MAAM,MAAM,GAAG,CAAC;AAC3E,UAAM,eAAe,KAAK,IAAI,SAAS,OAAO,eAAe,OAAO,MAAM,GAAG,CAAC;AAE9E,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAIZ,QAAI,QAAQ,SAAS,SAAS,OAAO;AACnC,cAAQ,gBAAgB,CAAC;AAAA,IAC3B,OAAO;AACL,cAAQ,MAAM,IAAI,KAAK,kBAAkB,SAAS,OAAO,eAAe,OAAO,MAAM,IAAI;AAAA,IAC3F;AACA,QAAI,QAAQ,UAAU,SAAS,QAAQ;AACrC,cAAQ,eAAe,CAAC;AAAA,IAC1B,OAAO;AACL,cAAQ,MAAM,IAAI,KAAK,kBAAkB,SAAS,MAAM,eAAe,MAAM,MAAM,IAAI;AAAA,IACzF;AACA,SAAK,sBAAsB;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,WAAO;AAAA,MACL,GAAG,MAAM,IAAI;AAAA,MACb,GAAG,MAAM,IAAI;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,UAAU,aAAa;AACpC,SAAK,oBAAoB,QAAQ;AACjC,SAAK,yBAAyB,aAAa,QAAQ;AACnD,SAAK,sBAAsB,aAAa,QAAQ;AAChD,QAAI,SAAS,YAAY;AACvB,WAAK,iBAAiB,SAAS,UAAU;AAAA,IAC3C;AAIA,QAAI,KAAK,iBAAiB,UAAU,QAAQ;AAC1C,YAAM,mBAAmB,KAAK,qBAAqB;AAGnD,UAAI,aAAa,KAAK,iBAAiB,CAAC,KAAK,yBAAyB,CAAC,wBAAwB,KAAK,uBAAuB,gBAAgB,GAAG;AAC5I,cAAM,cAAc,IAAI,+BAA+B,UAAU,gBAAgB;AACjF,aAAK,iBAAiB,KAAK,WAAW;AAAA,MACxC;AACA,WAAK,wBAAwB;AAAA,IAC/B;AAEA,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,QAAI,CAAC,KAAK,0BAA0B;AAClC;AAAA,IACF;AACA,UAAM,WAAW,KAAK,aAAa,iBAAiB,KAAK,wBAAwB;AACjF,QAAI;AACJ,QAAI,UAAU,SAAS;AACvB,QAAI,SAAS,aAAa,UAAU;AAClC,gBAAU;AAAA,IACZ,WAAW,KAAK,OAAO,GAAG;AACxB,gBAAU,SAAS,aAAa,UAAU,UAAU;AAAA,IACtD,OAAO;AACL,gBAAU,SAAS,aAAa,UAAU,SAAS;AAAA,IACrD;AACA,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,eAAS,CAAC,EAAE,MAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B,QAAQ,UAAU;AAC1C,UAAM,WAAW,KAAK;AACtB,UAAM,QAAQ,KAAK,OAAO;AAC1B,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,aAAa,OAAO;AAE/B,YAAM,OAAO;AACb,eAAS,SAAS,SAAS,MAAM,KAAK;AAAA,IACxC,WAAW,SAAS,aAAa,UAAU;AAIzC,eAAS,SAAS,SAAS,OAAO,IAAI,KAAK,kBAAkB;AAC7D,eAAS,SAAS,SAAS,SAAS,KAAK;AAAA,IAC3C,OAAO;AAKL,YAAM,iCAAiC,KAAK,IAAI,SAAS,SAAS,OAAO,IAAI,SAAS,KAAK,OAAO,CAAC;AACnG,YAAM,iBAAiB,KAAK,qBAAqB;AACjD,eAAS,iCAAiC;AAC1C,YAAM,OAAO,IAAI;AACjB,UAAI,SAAS,kBAAkB,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB;AAC7E,cAAM,OAAO,IAAI,iBAAiB;AAAA,MACpC;AAAA,IACF;AAEA,UAAM,+BAA+B,SAAS,aAAa,WAAW,CAAC,SAAS,SAAS,aAAa,SAAS;AAE/G,UAAM,8BAA8B,SAAS,aAAa,SAAS,CAAC,SAAS,SAAS,aAAa,WAAW;AAC9G,QAAI,OAAO,MAAM;AACjB,QAAI,6BAA6B;AAC/B,cAAQ,SAAS,QAAQ,OAAO,IAAI,KAAK,kBAAkB;AAC3D,cAAQ,OAAO,IAAI,KAAK;AAAA,IAC1B,WAAW,8BAA8B;AACvC,aAAO,OAAO;AACd,cAAQ,SAAS,QAAQ,OAAO;AAAA,IAClC,OAAO;AAKL,YAAM,iCAAiC,KAAK,IAAI,SAAS,QAAQ,OAAO,IAAI,SAAS,MAAM,OAAO,CAAC;AACnG,YAAM,gBAAgB,KAAK,qBAAqB;AAChD,cAAQ,iCAAiC;AACzC,aAAO,OAAO,IAAI;AAClB,UAAI,QAAQ,iBAAiB,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB;AAC3E,eAAO,OAAO,IAAI,gBAAgB;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,QAAQ,UAAU;AACtC,UAAM,kBAAkB,KAAK,0BAA0B,QAAQ,QAAQ;AAGvE,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB;AAClD,sBAAgB,SAAS,KAAK,IAAI,gBAAgB,QAAQ,KAAK,qBAAqB,MAAM;AAC1F,sBAAgB,QAAQ,KAAK,IAAI,gBAAgB,OAAO,KAAK,qBAAqB,KAAK;AAAA,IACzF;AACA,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,kBAAkB,GAAG;AAC5B,aAAO,MAAM,OAAO,OAAO;AAC3B,aAAO,SAAS,OAAO,QAAQ,OAAO,YAAY,OAAO,WAAW;AACpE,aAAO,QAAQ,OAAO,SAAS;AAAA,IACjC,OAAO;AACL,YAAM,YAAY,KAAK,YAAY,UAAU,EAAE;AAC/C,YAAM,WAAW,KAAK,YAAY,UAAU,EAAE;AAC9C,aAAO,SAAS,oBAAoB,gBAAgB,MAAM;AAC1D,aAAO,MAAM,oBAAoB,gBAAgB,GAAG;AACpD,aAAO,SAAS,oBAAoB,gBAAgB,MAAM;AAC1D,aAAO,QAAQ,oBAAoB,gBAAgB,KAAK;AACxD,aAAO,OAAO,oBAAoB,gBAAgB,IAAI;AACtD,aAAO,QAAQ,oBAAoB,gBAAgB,KAAK;AAExD,UAAI,SAAS,aAAa,UAAU;AAClC,eAAO,aAAa;AAAA,MACtB,OAAO;AACL,eAAO,aAAa,SAAS,aAAa,QAAQ,aAAa;AAAA,MACjE;AACA,UAAI,SAAS,aAAa,UAAU;AAClC,eAAO,iBAAiB;AAAA,MAC1B,OAAO;AACL,eAAO,iBAAiB,SAAS,aAAa,WAAW,aAAa;AAAA,MACxE;AACA,UAAI,WAAW;AACb,eAAO,YAAY,oBAAoB,SAAS;AAAA,MAClD;AACA,UAAI,UAAU;AACZ,eAAO,WAAW,oBAAoB,QAAQ;AAAA,MAChD;AAAA,IACF;AACA,SAAK,uBAAuB;AAC5B,iBAAa,KAAK,aAAa,OAAO,MAAM;AAAA,EAC9C;AAAA;AAAA,EAEA,0BAA0B;AACxB,iBAAa,KAAK,aAAa,OAAO;AAAA,MACpC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,6BAA6B;AAC3B,iBAAa,KAAK,MAAM,OAAO;AAAA,MAC7B,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,yBAAyB,aAAa,UAAU;AAC9C,UAAM,SAAS,CAAC;AAChB,UAAM,mBAAmB,KAAK,kBAAkB;AAChD,UAAM,wBAAwB,KAAK;AACnC,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,QAAI,kBAAkB;AACpB,YAAM,iBAAiB,KAAK,eAAe,0BAA0B;AACrE,mBAAa,QAAQ,KAAK,kBAAkB,UAAU,aAAa,cAAc,CAAC;AAClF,mBAAa,QAAQ,KAAK,kBAAkB,UAAU,aAAa,cAAc,CAAC;AAAA,IACpF,OAAO;AACL,aAAO,WAAW;AAAA,IACpB;AAMA,QAAI,kBAAkB;AACtB,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAC3C,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAC3C,QAAI,SAAS;AACX,yBAAmB,cAAc,OAAO;AAAA,IAC1C;AACA,QAAI,SAAS;AACX,yBAAmB,cAAc,OAAO;AAAA,IAC1C;AACA,WAAO,YAAY,gBAAgB,KAAK;AAMxC,QAAI,OAAO,WAAW;AACpB,UAAI,kBAAkB;AACpB,eAAO,YAAY,oBAAoB,OAAO,SAAS;AAAA,MACzD,WAAW,uBAAuB;AAChC,eAAO,YAAY;AAAA,MACrB;AAAA,IACF;AACA,QAAI,OAAO,UAAU;AACnB,UAAI,kBAAkB;AACpB,eAAO,WAAW,oBAAoB,OAAO,QAAQ;AAAA,MACvD,WAAW,uBAAuB;AAChC,eAAO,WAAW;AAAA,MACpB;AAAA,IACF;AACA,iBAAa,KAAK,MAAM,OAAO,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB,UAAU,aAAa,gBAAgB;AAGvD,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AACA,QAAI,eAAe,KAAK,iBAAiB,aAAa,KAAK,cAAc,QAAQ;AACjF,QAAI,KAAK,WAAW;AAClB,qBAAe,KAAK,qBAAqB,cAAc,KAAK,cAAc,cAAc;AAAA,IAC1F;AAGA,QAAI,SAAS,aAAa,UAAU;AAGlC,YAAM,iBAAiB,KAAK,UAAU,gBAAgB;AACtD,aAAO,SAAS,GAAG,kBAAkB,aAAa,IAAI,KAAK,aAAa,OAAO;AAAA,IACjF,OAAO;AACL,aAAO,MAAM,oBAAoB,aAAa,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,UAAU,aAAa,gBAAgB;AAGvD,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AACA,QAAI,eAAe,KAAK,iBAAiB,aAAa,KAAK,cAAc,QAAQ;AACjF,QAAI,KAAK,WAAW;AAClB,qBAAe,KAAK,qBAAqB,cAAc,KAAK,cAAc,cAAc;AAAA,IAC1F;AAKA,QAAI;AACJ,QAAI,KAAK,OAAO,GAAG;AACjB,gCAA0B,SAAS,aAAa,QAAQ,SAAS;AAAA,IACnE,OAAO;AACL,gCAA0B,SAAS,aAAa,QAAQ,UAAU;AAAA,IACpE;AAGA,QAAI,4BAA4B,SAAS;AACvC,YAAM,gBAAgB,KAAK,UAAU,gBAAgB;AACrD,aAAO,QAAQ,GAAG,iBAAiB,aAAa,IAAI,KAAK,aAAa,MAAM;AAAA,IAC9E,OAAO;AACL,aAAO,OAAO,oBAAoB,aAAa,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AAErB,UAAM,eAAe,KAAK,eAAe;AACzC,UAAM,gBAAgB,KAAK,MAAM,sBAAsB;AAIvD,UAAM,wBAAwB,KAAK,aAAa,IAAI,gBAAc;AAChE,aAAO,WAAW,cAAc,EAAE,cAAc,sBAAsB;AAAA,IACxE,CAAC;AACD,WAAO;AAAA,MACL,iBAAiB,4BAA4B,cAAc,qBAAqB;AAAA,MAChF,qBAAqB,6BAA6B,cAAc,qBAAqB;AAAA,MACrF,kBAAkB,4BAA4B,eAAe,qBAAqB;AAAA,MAClF,sBAAsB,6BAA6B,eAAe,qBAAqB;AAAA,IACzF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,WAAW,WAAW;AACvC,WAAO,UAAU,OAAO,CAAC,cAAc,oBAAoB;AACzD,aAAO,eAAe,KAAK,IAAI,iBAAiB,CAAC;AAAA,IACnD,GAAG,MAAM;AAAA,EACX;AAAA;AAAA,EAEA,2BAA2B;AAMzB,UAAM,QAAQ,KAAK,UAAU,gBAAgB;AAC7C,UAAM,SAAS,KAAK,UAAU,gBAAgB;AAC9C,UAAM,iBAAiB,KAAK,eAAe,0BAA0B;AACrE,WAAO;AAAA,MACL,KAAK,eAAe,MAAM,KAAK;AAAA,MAC/B,MAAM,eAAe,OAAO,KAAK;AAAA,MACjC,OAAO,eAAe,OAAO,QAAQ,KAAK;AAAA,MAC1C,QAAQ,eAAe,MAAM,SAAS,KAAK;AAAA,MAC3C,OAAO,QAAQ,IAAI,KAAK;AAAA,MACxB,QAAQ,SAAS,IAAI,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,YAAY,aAAa,MAAM;AAAA,EAC7C;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,CAAC,KAAK,0BAA0B,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,WAAW,UAAU,MAAM;AACzB,QAAI,SAAS,KAAK;AAGhB,aAAO,SAAS,WAAW,OAAO,KAAK,WAAW,SAAS;AAAA,IAC7D;AACA,WAAO,SAAS,WAAW,OAAO,KAAK,WAAW,SAAS;AAAA,EAC7D;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,oBAAoB,QAAQ;AACpC,cAAM,MAAM,uEAAuE;AAAA,MACrF;AAGA,WAAK,oBAAoB,QAAQ,UAAQ;AACvC,mCAA2B,WAAW,KAAK,OAAO;AAClD,iCAAyB,WAAW,KAAK,OAAO;AAChD,mCAA2B,YAAY,KAAK,QAAQ;AACpD,iCAAyB,YAAY,KAAK,QAAQ;AAAA,MACpD,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,QAAI,KAAK,OAAO;AACd,kBAAY,UAAU,EAAE,QAAQ,cAAY;AAC1C,YAAI,aAAa,MAAM,KAAK,qBAAqB,QAAQ,QAAQ,MAAM,IAAI;AACzE,eAAK,qBAAqB,KAAK,QAAQ;AACvC,eAAK,MAAM,UAAU,IAAI,QAAQ;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,OAAO;AACd,WAAK,qBAAqB,QAAQ,cAAY;AAC5C,aAAK,MAAM,UAAU,OAAO,QAAQ;AAAA,MACtC,CAAC;AACD,WAAK,uBAAuB,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,SAAS,KAAK;AACpB,QAAI,kBAAkB,YAAY;AAChC,aAAO,OAAO,cAAc,sBAAsB;AAAA,IACpD;AAEA,QAAI,kBAAkB,SAAS;AAC7B,aAAO,OAAO,sBAAsB;AAAA,IACtC;AACA,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,SAAS,OAAO,UAAU;AAEhC,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,QAAQ,OAAO,IAAI;AAAA,MACnB,MAAM,OAAO;AAAA,MACb,OAAO,OAAO,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,aAAa,aAAa,QAAQ;AACzC,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,kBAAY,GAAG,IAAI,OAAO,GAAG;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,SAAS,MAAM;AAC9C,UAAM,CAAC,OAAO,KAAK,IAAI,MAAM,MAAM,cAAc;AACjD,WAAO,CAAC,SAAS,UAAU,OAAO,WAAW,KAAK,IAAI;AAAA,EACxD;AACA,SAAO,SAAS;AAClB;AAOA,SAAS,6BAA6B,YAAY;AAChD,SAAO;AAAA,IACL,KAAK,KAAK,MAAM,WAAW,GAAG;AAAA,IAC9B,OAAO,KAAK,MAAM,WAAW,KAAK;AAAA,IAClC,QAAQ,KAAK,MAAM,WAAW,MAAM;AAAA,IACpC,MAAM,KAAK,MAAM,WAAW,IAAI;AAAA,IAChC,OAAO,KAAK,MAAM,WAAW,KAAK;AAAA,IAClC,QAAQ,KAAK,MAAM,WAAW,MAAM;AAAA,EACtC;AACF;AAEA,SAAS,wBAAwB,GAAG,GAAG;AACrC,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,SAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,yBAAyB,EAAE;AACjL;AA6CA,IAAM,eAAe;AAOrB,IAAM,yBAAN,MAA6B;AAAA;AAAA,EAE3B;AAAA,EACA,eAAe;AAAA,EACf,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,OAAO,YAAY;AACjB,UAAM,SAAS,WAAW,UAAU;AACpC,SAAK,cAAc;AACnB,QAAI,KAAK,UAAU,CAAC,OAAO,OAAO;AAChC,iBAAW,WAAW;AAAA,QACpB,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW,CAAC,OAAO,QAAQ;AAClC,iBAAW,WAAW;AAAA,QACpB,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AACA,eAAW,YAAY,UAAU,IAAI,YAAY;AACjD,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,IAAI;AACd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,QAAQ,IAAI;AACf,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,IAAI;AACjB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,IAAI;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,IAAI;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,IAAI;AACd,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,QAAQ,IAAI;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,WAAW;AAAA,QAC1B,OAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,IAAI;AACjB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,WAAW;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,SAAS,IAAI;AAC9B,SAAK,KAAK,MAAM;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,SAAS,IAAI;AAC5B,SAAK,IAAI,MAAM;AACf,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAIN,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,YAAY,GAAG;AACxD;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,eAAe;AAC/C,UAAM,eAAe,KAAK,YAAY,YAAY;AAClD,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,6BAA6B,UAAU,UAAU,UAAU,aAAa,CAAC,YAAY,aAAa,UAAU,aAAa;AAC/H,UAAM,2BAA2B,WAAW,UAAU,WAAW,aAAa,CAAC,aAAa,cAAc,UAAU,cAAc;AAClI,UAAM,YAAY,KAAK;AACvB,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,KAAK,YAAY,UAAU,EAAE,cAAc;AACzD,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,iBAAiB;AACrB,QAAI,2BAA2B;AAC7B,uBAAiB;AAAA,IACnB,WAAW,cAAc,UAAU;AACjC,uBAAiB;AACjB,UAAI,OAAO;AACT,sBAAc;AAAA,MAChB,OAAO;AACL,qBAAa;AAAA,MACf;AAAA,IACF,WAAW,OAAO;AAChB,UAAI,cAAc,UAAU,cAAc,OAAO;AAC/C,yBAAiB;AACjB,qBAAa;AAAA,MACf,WAAW,cAAc,WAAW,cAAc,SAAS;AACzD,yBAAiB;AACjB,sBAAc;AAAA,MAChB;AAAA,IACF,WAAW,cAAc,UAAU,cAAc,SAAS;AACxD,uBAAiB;AACjB,mBAAa;AAAA,IACf,WAAW,cAAc,WAAW,cAAc,OAAO;AACvD,uBAAiB;AACjB,oBAAc;AAAA,IAChB;AACA,WAAO,WAAW,KAAK;AACvB,WAAO,aAAa,4BAA4B,MAAM;AACtD,WAAO,YAAY,0BAA0B,MAAM,KAAK;AACxD,WAAO,eAAe,KAAK;AAC3B,WAAO,cAAc,4BAA4B,MAAM;AACvD,iBAAa,iBAAiB;AAC9B,iBAAa,aAAa,0BAA0B,eAAe,KAAK;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,eAAe,CAAC,KAAK,aAAa;AACzC;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,eAAe;AAC/C,UAAM,SAAS,KAAK,YAAY;AAChC,UAAM,eAAe,OAAO;AAC5B,WAAO,UAAU,OAAO,YAAY;AACpC,iBAAa,iBAAiB,aAAa,aAAa,OAAO,YAAY,OAAO,eAAe,OAAO,aAAa,OAAO,cAAc,OAAO,WAAW;AAC5J,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AACF;AAGA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,iBAAiB,OAAO,aAAa;AAAA,EACrC,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAIf,SAAS;AACP,WAAO,IAAI,uBAAuB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,QAAQ;AAC1B,WAAO,IAAI,kCAAkC,QAAQ,KAAK,gBAAgB,KAAK,WAAW,KAAK,WAAW,KAAK,iBAAiB;AAAA,EAClI;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,IAChC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,mBAAmB,OAAO,qBAAqB;AAAA,EAC/C,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,mBAAmB,OAAO,sBAAsB;AAAA,EAChD,sBAAsB,OAAO,yBAAyB;AAAA,EACtD,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,kBAAkB,OAAO,cAAc;AAAA,EACvC,YAAY,OAAO,QAAQ;AAAA,EAC3B,0BAA0B,OAAO,6BAA6B;AAAA,EAC9D,wBAAwB,OAAO,uBAAuB;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,YAAY;AAAA,EAClC,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D;AAAA,EACA,eAAe,OAAO,sBAAsB;AAAA,EAC5C,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf,OAAO,QAAQ;AAGb,SAAK,aAAa,KAAK,sBAAsB;AAC7C,UAAM,OAAO,KAAK,mBAAmB;AACrC,UAAM,OAAO,KAAK,mBAAmB,IAAI;AACzC,UAAM,eAAe,KAAK,oBAAoB,IAAI;AAClD,UAAM,gBAAgB,IAAI,cAAc,MAAM;AAC9C,kBAAc,YAAY,cAAc,aAAa,KAAK,gBAAgB;AAC1E,WAAO,IAAI,WAAW,cAAc,MAAM,MAAM,eAAe,KAAK,SAAS,KAAK,qBAAqB,KAAK,WAAW,KAAK,WAAW,KAAK,yBAAyB,KAAK,0BAA0B,kBAAkB,KAAK,UAAU,IAAI,mBAAmB,GAAG,KAAK,SAAS;AAAA,EAC/Q;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,MAAM;AACvB,UAAM,OAAO,KAAK,UAAU,cAAc,KAAK;AAC/C,SAAK,KAAK,KAAK,aAAa,MAAM,cAAc;AAChD,SAAK,UAAU,IAAI,kBAAkB;AACrC,SAAK,YAAY,IAAI;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,UAAM,OAAO,KAAK,UAAU,cAAc,KAAK;AAC/C,SAAK,kBAAkB,oBAAoB,EAAE,YAAY,IAAI;AAC7D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,MAAM;AAGxB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,KAAK,UAAU,IAAI,cAAc;AAAA,IAClD;AACA,WAAO,IAAI,gBAAgB,MAAM,MAAM,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS;AAAA,EACrF;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,SAAQ;AAAA,IACjB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,sBAAsB,CAAC;AAAA,EAC3B,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,CAAC;AAED,IAAM,wCAAwC,IAAI,eAAe,yCAAyC;AAAA,EACxG,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAKD,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,GAAG,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACpG,UAAU,CAAC,kBAAkB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,WAAW,OAAO,OAAO;AAAA,EACzB,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA,wBAAwB,aAAa;AAAA,EACrC,sBAAsB,aAAa;AAAA,EACnC,sBAAsB,aAAa;AAAA,EACnC,wBAAwB,aAAa;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA,yBAAyB,OAAO,qCAAqC;AAAA,EACrE,uBAAuB;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,eAAe;AAAA;AAAA,EAEf;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd,eAAe;AAAA;AAAA,EAEf,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA;AAAA,EAEhB,OAAO;AAAA;AAAA,EAEP,IAAI,sBAAsB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoB,OAAO;AAC7B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,gBAAgB,IAAI,aAAa;AAAA;AAAA,EAEjC,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC,cAAc;AACZ,UAAM,cAAc,OAAO,WAAW;AACtC,UAAM,mBAAmB,OAAO,gBAAgB;AAChD,SAAK,kBAAkB,IAAI,eAAe,aAAa,gBAAgB;AACvE,SAAK,iBAAiB,KAAK,uBAAuB;AAAA,EACpD;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK,OAAO,KAAK,KAAK,QAAQ;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,SAAK,oBAAoB,YAAY;AACrC,SAAK,oBAAoB,YAAY;AACrC,SAAK,sBAAsB,YAAY;AACvC,SAAK,sBAAsB,YAAY;AACvC,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,SAAS;AAC3C,WAAK,aAAa,WAAW;AAAA,QAC3B,OAAO,KAAK;AAAA,QACZ,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK;AAAA,MAClB,CAAC;AACD,UAAI,QAAQ,QAAQ,KAAK,KAAK,MAAM;AAClC,aAAK,UAAU,MAAM;AAAA,MACvB;AAAA,IACF;AACA,QAAI,QAAQ,MAAM,GAAG;AACnB,WAAK,OAAO,KAAK,cAAc,IAAI,KAAK,cAAc;AAAA,IACxD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,QAAQ;AAC7C,WAAK,YAAY;AAAA,IACnB;AACA,UAAM,aAAa,KAAK,cAAc,KAAK,SAAS,OAAO,KAAK,aAAa,CAAC;AAC9E,SAAK,sBAAsB,WAAW,YAAY,EAAE,UAAU,MAAM,KAAK,OAAO,KAAK,CAAC;AACtF,SAAK,sBAAsB,WAAW,YAAY,EAAE,UAAU,MAAM,KAAK,OAAO,KAAK,CAAC;AACtF,eAAW,cAAc,EAAE,UAAU,WAAS;AAC5C,WAAK,eAAe,KAAK,KAAK;AAC9B,UAAI,MAAM,YAAY,UAAU,CAAC,KAAK,gBAAgB,CAAC,eAAe,KAAK,GAAG;AAC5E,cAAM,eAAe;AACrB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,CAAC;AACD,SAAK,YAAY,qBAAqB,EAAE,UAAU,WAAS;AACzD,YAAM,SAAS,KAAK,kBAAkB;AACtC,YAAM,SAAS,gBAAgB,KAAK;AACpC,UAAI,CAAC,UAAU,WAAW,UAAU,CAAC,OAAO,SAAS,MAAM,GAAG;AAC5D,aAAK,oBAAoB,KAAK,KAAK;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe;AACb,UAAM,mBAAmB,KAAK,YAAY,KAAK,oBAAoB,KAAK,wBAAwB;AAChG,UAAM,gBAAgB,IAAI,cAAc;AAAA,MACtC,WAAW,KAAK,QAAQ;AAAA,MACxB;AAAA,MACA,gBAAgB,KAAK;AAAA,MACrB,aAAa,KAAK;AAAA,MAClB,qBAAqB,KAAK;AAAA,IAC5B,CAAC;AACD,QAAI,KAAK,SAAS,KAAK,UAAU,GAAG;AAClC,oBAAc,QAAQ,KAAK;AAAA,IAC7B;AACA,QAAI,KAAK,UAAU,KAAK,WAAW,GAAG;AACpC,oBAAc,SAAS,KAAK;AAAA,IAC9B;AACA,QAAI,KAAK,YAAY,KAAK,aAAa,GAAG;AACxC,oBAAc,WAAW,KAAK;AAAA,IAChC;AACA,QAAI,KAAK,aAAa,KAAK,cAAc,GAAG;AAC1C,oBAAc,YAAY,KAAK;AAAA,IACjC;AACA,QAAI,KAAK,eAAe;AACtB,oBAAc,gBAAgB,KAAK;AAAA,IACrC;AACA,QAAI,KAAK,YAAY;AACnB,oBAAc,aAAa,KAAK;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,kBAAkB;AACxC,UAAM,YAAY,KAAK,UAAU,IAAI,sBAAoB;AAAA,MACvD,SAAS,gBAAgB;AAAA,MACzB,SAAS,gBAAgB;AAAA,MACzB,UAAU,gBAAgB;AAAA,MAC1B,UAAU,gBAAgB;AAAA,MAC1B,SAAS,gBAAgB,WAAW,KAAK;AAAA,MACzC,SAAS,gBAAgB,WAAW,KAAK;AAAA,MACzC,YAAY,gBAAgB,cAAc;AAAA,IAC5C,EAAE;AACF,WAAO,iBAAiB,UAAU,KAAK,WAAW,CAAC,EAAE,cAAc,SAAS,EAAE,uBAAuB,KAAK,kBAAkB,EAAE,SAAS,KAAK,IAAI,EAAE,kBAAkB,KAAK,aAAa,EAAE,mBAAmB,KAAK,cAAc,EAAE,mBAAmB,KAAK,YAAY,EAAE,sBAAsB,KAAK,uBAAuB;AAAA,EAC1T;AAAA;AAAA,EAEA,0BAA0B;AACxB,UAAM,WAAW,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,WAAW,CAAC;AAC/E,SAAK,wBAAwB,QAAQ;AACrC,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,QAAI,KAAK,kBAAkB,kBAAkB;AAC3C,aAAO,KAAK,OAAO;AAAA,IACrB,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,kBAAkB,kBAAkB;AAC3C,aAAO,KAAK,OAAO,WAAW;AAAA,IAChC;AACA,QAAI,KAAK,kBAAkB,YAAY;AACrC,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,QAAI,OAAO,YAAY,eAAe,KAAK,kBAAkB,SAAS;AACpE,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,eAAe;AAAA,IACtB,OAAO;AAEL,WAAK,YAAY,UAAU,EAAE,cAAc,KAAK;AAAA,IAClD;AACA,QAAI,CAAC,KAAK,YAAY,YAAY,GAAG;AACnC,WAAK,YAAY,OAAO,KAAK,eAAe;AAAA,IAC9C;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,wBAAwB,KAAK,YAAY,cAAc,EAAE,UAAU,WAAS;AAC/E,aAAK,cAAc,KAAK,KAAK;AAAA,MAC/B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,sBAAsB,YAAY;AAAA,IACzC;AACA,SAAK,sBAAsB,YAAY;AAGvC,QAAI,KAAK,eAAe,UAAU,SAAS,GAAG;AAC5C,WAAK,wBAAwB,KAAK,UAAU,gBAAgB,KAAK,UAAU,MAAM,KAAK,eAAe,UAAU,SAAS,CAAC,CAAC,EAAE,UAAU,cAAY;AAChJ,aAAK,QAAQ,IAAI,MAAM,KAAK,eAAe,KAAK,QAAQ,CAAC;AACzD,YAAI,KAAK,eAAe,UAAU,WAAW,GAAG;AAC9C,eAAK,sBAAsB,YAAY;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,aAAa,OAAO;AACzB,SAAK,sBAAsB,YAAY;AACvC,SAAK,sBAAsB,YAAY;AACvC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,yBAAyB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,IAC7G,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,6BAA6B,QAAQ;AAAA,MACjD,WAAW,CAAC,GAAG,gCAAgC,WAAW;AAAA,MAC1D,kBAAkB,CAAC,GAAG,uCAAuC,kBAAkB;AAAA,MAC/E,SAAS,CAAC,GAAG,8BAA8B,SAAS;AAAA,MACpD,SAAS,CAAC,GAAG,8BAA8B,SAAS;AAAA,MACpD,OAAO,CAAC,GAAG,4BAA4B,OAAO;AAAA,MAC9C,QAAQ,CAAC,GAAG,6BAA6B,QAAQ;AAAA,MACjD,UAAU,CAAC,GAAG,+BAA+B,UAAU;AAAA,MACvD,WAAW,CAAC,GAAG,gCAAgC,WAAW;AAAA,MAC1D,eAAe,CAAC,GAAG,oCAAoC,eAAe;AAAA,MACtE,YAAY,CAAC,GAAG,iCAAiC,YAAY;AAAA,MAC7D,gBAAgB,CAAC,GAAG,qCAAqC,gBAAgB;AAAA,MACzE,gBAAgB,CAAC,GAAG,qCAAqC,gBAAgB;AAAA,MACzE,MAAM,CAAC,GAAG,2BAA2B,MAAM;AAAA,MAC3C,cAAc,CAAC,GAAG,mCAAmC,cAAc;AAAA,MACnE,yBAAyB,CAAC,GAAG,wCAAwC,yBAAyB;AAAA,MAC9F,aAAa,CAAC,GAAG,kCAAkC,eAAe,gBAAgB;AAAA,MAClF,cAAc,CAAC,GAAG,mCAAmC,gBAAgB,gBAAgB;AAAA,MACrF,oBAAoB,CAAC,GAAG,yCAAyC,sBAAsB,gBAAgB;AAAA,MACvG,eAAe,CAAC,GAAG,oCAAoC,iBAAiB,gBAAgB;AAAA,MACxF,MAAM,CAAC,GAAG,2BAA2B,QAAQ,gBAAgB;AAAA,MAC7D,qBAAqB,CAAC,GAAG,0CAA0C,uBAAuB,gBAAgB;AAAA,IAC5G;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAC,qBAAqB;AAAA,IAChC,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,IAC9C,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,sCAAsC;AAAA,IAC/C,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,uDAAuD,SAAS;AACvE,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AACnD;AAMA,IAAM,iDAAiD;AAAA,EACrD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,cAAc,iBAAiB,qBAAqB,gBAAgB;AAAA,IAC1F,SAAS,CAAC,qBAAqB,kBAAkB,eAAe;AAAA,EAClE,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,SAAS,8CAA8C;AAAA,IACnE,SAAS,CAAC,YAAY,cAAc,iBAAiB,eAAe;AAAA,EACtE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,cAAc,iBAAiB,qBAAqB,gBAAgB;AAAA,MAC1F,SAAS,CAAC,qBAAqB,kBAAkB,eAAe;AAAA,MAChE,WAAW,CAAC,SAAS,8CAA8C;AAAA,IACrE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACtoGH,IAAM,6BAAN,MAAM,oCAAmC,iBAAiB;AAAA,EACxD,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM;AAAA,EACR;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,mBAAmB;AACjB,UAAM,YAAY,KAAK,cAAc;AACrC,UAAM,iBAAiB;AACvB,SAAK,iCAAiC;AACtC,QAAI,WAAW;AACb,WAAK,6BAA6B;AAClC,WAAK,6BAA6B,KAAK,UAAU,OAAO,YAAY,WAAW,MAAM;AACnF,aAAK,iCAAiC;AAAA,MACxC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mCAAmC;AACjC,QAAI,KAAK,mBAAmB;AAC1B,YAAM,oBAAoB,KAAK,qBAAqB;AACpD,YAAM,SAAS,qBAAqB,KAAK,UAAU;AACnD,aAAO,YAAY,KAAK,iBAAiB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,sBAAsB;AAC9B,YAAM,YAAY,KAAK;AACvB,UAAI,UAAU,mBAAmB;AAC/B,aAAK,uBAAuB;AAAA,MAC9B,WAAW,UAAU,yBAAyB;AAC5C,aAAK,uBAAuB;AAAA,MAC9B,WAAW,UAAU,sBAAsB;AACzC,aAAK,uBAAuB;AAAA,MAC9B,WAAW,UAAU,qBAAqB;AACxC,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,YAAY,KAAK;AACvB,WAAO,UAAU,qBAAqB,UAAU,2BAA2B,UAAU,wBAAwB,UAAU,uBAAuB;AAAA,EAChJ;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,4BAA2B;AAAA,IACpC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": []}