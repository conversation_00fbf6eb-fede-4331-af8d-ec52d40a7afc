
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '769554f2be38c387c4c9a924c844ec592a4348389ad76c736307b1ccc9ca8e34', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: 'd856c42f10a62598269ceaf83303376c5111793cc784abebaeda226351be7c1d', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-UJ27JA5M.css': {size: 298495, hash: 'KuJzfxHS510', text: () => import('./assets-chunks/styles-UJ27JA5M_css.mjs').then(m => m.default)}
  },
};
