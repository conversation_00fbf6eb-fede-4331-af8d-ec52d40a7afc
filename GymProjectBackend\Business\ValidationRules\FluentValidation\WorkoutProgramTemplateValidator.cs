using Core.Utilities.Security.CompanyContext;
using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.DTOs;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class WorkoutProgramTemplateAddValidator : AbstractValidator<WorkoutProgramTemplateAddDto>
    {
        private readonly ICompanyContext _companyContext;
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;

        public WorkoutProgramTemplateAddValidator()
        {
            _companyContext = ServiceTool.ServiceProvider?.GetService<ICompanyContext>();
            _workoutProgramTemplateDal = ServiceTool.ServiceProvider?.GetService<IWorkoutProgramTemplateDal>();

            RuleFor(x => x.ProgramName).NotEmpty().WithMessage("Program adı boş bırakılamaz.");
            RuleFor(x => x.ProgramName).MinimumLength(2).WithMessage("Program adı en az 2 karakter olmalıdır.");
            RuleFor(x => x.ProgramName).MaximumLength(200).WithMessage("Program adı en fazla 200 karakter olabilir.");
            RuleFor(x => x.ProgramName).Must((template, name) => BeUniqueProgramName(template)).WithMessage("Bu isimde bir antrenman programı zaten mevcut.");
            
            RuleFor(x => x.Description).MaximumLength(1000).WithMessage("Açıklama en fazla 1000 karakter olabilir.");
            
            RuleFor(x => x.ExperienceLevel).MaximumLength(50).WithMessage("Deneyim seviyesi en fazla 50 karakter olabilir.");
            
            RuleFor(x => x.TargetGoal).MaximumLength(100).WithMessage("Hedef en fazla 100 karakter olabilir.");
            
            RuleFor(x => x.Days).NotEmpty().WithMessage("En az bir gün tanımlanmalıdır.");
            RuleFor(x => x.Days.Count).Equal(7).WithMessage("Antrenman programı tam olarak 7 gün olmalıdır.");
            RuleFor(x => x.Days).Must(HaveAtLeastOneWorkoutDay).WithMessage("En az bir gün egzersiz günü olmalıdır. Tüm günler dinlenme günü olamaz.");

            RuleForEach(x => x.Days).SetValidator(new WorkoutProgramDayAddValidator());
        }

        private bool BeUniqueProgramName(WorkoutProgramTemplateAddDto template)
        {
            // Null check for DI services
            if (_companyContext == null || _workoutProgramTemplateDal == null)
                return true; // Validation geçer, başka katmanda kontrol edilir

            // DI kullanılıyor - Scalability optimized
            int companyId = _companyContext.GetCompanyId();

            return !_workoutProgramTemplateDal.GetAll(t =>
                t.ProgramName == template.ProgramName &&
                t.IsActive == true &&
                t.CompanyID == companyId).Any();
        }

        private bool HaveAtLeastOneWorkoutDay(List<WorkoutProgramDayAddDto> days)
        {
            // En az bir gün egzersiz günü olmalı (IsRestDay = false)
            return days.Any(d => !d.IsRestDay);
        }
    }

    public class WorkoutProgramTemplateUpdateValidator : AbstractValidator<WorkoutProgramTemplateUpdateDto>
    {
        private readonly ICompanyContext _companyContext;
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;

        public WorkoutProgramTemplateUpdateValidator()
        {
            _companyContext = ServiceTool.ServiceProvider?.GetService<ICompanyContext>();
            _workoutProgramTemplateDal = ServiceTool.ServiceProvider?.GetService<IWorkoutProgramTemplateDal>();

            RuleFor(x => x.WorkoutProgramTemplateID).NotEmpty().WithMessage("Program ID boş bırakılamaz.");
            RuleFor(x => x.ProgramName).NotEmpty().WithMessage("Program adı boş bırakılamaz.");
            RuleFor(x => x.ProgramName).MinimumLength(2).WithMessage("Program adı en az 2 karakter olmalıdır.");
            RuleFor(x => x.ProgramName).MaximumLength(200).WithMessage("Program adı en fazla 200 karakter olabilir.");
            RuleFor(x => x.ProgramName).Must((template, name) => BeUniqueProgramNameForUpdate(template)).WithMessage("Bu isimde bir antrenman programı zaten mevcut.");
            
            RuleFor(x => x.Description).MaximumLength(1000).WithMessage("Açıklama en fazla 1000 karakter olabilir.");
            
            RuleFor(x => x.ExperienceLevel).MaximumLength(50).WithMessage("Deneyim seviyesi en fazla 50 karakter olabilir.");
            
            RuleFor(x => x.TargetGoal).MaximumLength(100).WithMessage("Hedef en fazla 100 karakter olabilir.");
            
            RuleFor(x => x.Days).NotEmpty().WithMessage("En az bir gün tanımlanmalıdır.");
            RuleFor(x => x.Days.Count).Equal(7).WithMessage("Antrenman programı tam olarak 7 gün olmalıdır.");
            RuleFor(x => x.Days).Must(HaveAtLeastOneWorkoutDayForUpdate).WithMessage("En az bir gün egzersiz günü olmalıdır. Tüm günler dinlenme günü olamaz.");

            RuleForEach(x => x.Days).SetValidator(new WorkoutProgramDayUpdateValidator());
        }

        private bool BeUniqueProgramNameForUpdate(WorkoutProgramTemplateUpdateDto template)
        {
            // Null check for DI services
            if (_companyContext == null || _workoutProgramTemplateDal == null)
                return true; // Validation geçer, başka katmanda kontrol edilir

            // DI kullanılıyor - Scalability optimized
            int companyId = _companyContext.GetCompanyId();

            return !_workoutProgramTemplateDal.GetAll(t =>
                t.ProgramName == template.ProgramName &&
                t.IsActive == true &&
                t.CompanyID == companyId &&
                t.WorkoutProgramTemplateID != template.WorkoutProgramTemplateID).Any();
        }

        private bool HaveAtLeastOneWorkoutDayForUpdate(List<WorkoutProgramDayUpdateDto> days)
        {
            // En az bir gün egzersiz günü olmalı (IsRestDay = false)
            return days.Any(d => !d.IsRestDay);
        }
    }

    public class WorkoutProgramDayAddValidator : AbstractValidator<WorkoutProgramDayAddDto>
    {
        public WorkoutProgramDayAddValidator()
        {
            RuleFor(x => x.DayNumber).InclusiveBetween(1, 7).WithMessage("Gün numarası 1-7 arasında olmalıdır.");
            RuleFor(x => x.DayName).NotEmpty().WithMessage("Gün adı boş bırakılamaz.");
            RuleFor(x => x.DayName).MaximumLength(100).WithMessage("Gün adı en fazla 100 karakter olabilir.");
            
            RuleForEach(x => x.Exercises).SetValidator(new WorkoutProgramExerciseAddValidator());
        }
    }

    public class WorkoutProgramDayUpdateValidator : AbstractValidator<WorkoutProgramDayUpdateDto>
    {
        public WorkoutProgramDayUpdateValidator()
        {
            RuleFor(x => x.DayNumber).InclusiveBetween(1, 7).WithMessage("Gün numarası 1-7 arasında olmalıdır.");
            RuleFor(x => x.DayName).NotEmpty().WithMessage("Gün adı boş bırakılamaz.");
            RuleFor(x => x.DayName).MaximumLength(100).WithMessage("Gün adı en fazla 100 karakter olabilir.");
            
            RuleForEach(x => x.Exercises).SetValidator(new WorkoutProgramExerciseUpdateValidator());
        }
    }

    public class WorkoutProgramExerciseAddValidator : AbstractValidator<WorkoutProgramExerciseAddDto>
    {
        public WorkoutProgramExerciseAddValidator()
        {
            RuleFor(x => x.ExerciseType).NotEmpty().WithMessage("Egzersiz türü boş bırakılamaz.");
            RuleFor(x => x.ExerciseType).Must(type => type == "System" || type == "Company").WithMessage("Egzersiz türü 'System' veya 'Company' olmalıdır.");
            RuleFor(x => x.ExerciseID).GreaterThan(0).WithMessage("Egzersiz seçimi yapılmalıdır.");
            RuleFor(x => x.OrderIndex).GreaterThan(0).WithMessage("Egzersiz sırası 1'den büyük olmalıdır.");
            RuleFor(x => x.Sets).GreaterThan(0).WithMessage("Set sayısı 1'den büyük olmalıdır.");
            RuleFor(x => x.Sets).LessThanOrEqualTo(20).WithMessage("Set sayısı 20'den fazla olamaz.");
            RuleFor(x => x.Reps).NotEmpty().WithMessage("Tekrar sayısı boş bırakılamaz.");
            RuleFor(x => x.Reps).MaximumLength(50).WithMessage("Tekrar sayısı en fazla 50 karakter olabilir.");
            RuleFor(x => x.RestTime).GreaterThanOrEqualTo(0).When(x => x.RestTime.HasValue).WithMessage("Dinlenme süresi negatif olamaz.");
            RuleFor(x => x.Notes).MaximumLength(500).WithMessage("Notlar en fazla 500 karakter olabilir.");
        }
    }

    public class WorkoutProgramExerciseUpdateValidator : AbstractValidator<WorkoutProgramExerciseUpdateDto>
    {
        public WorkoutProgramExerciseUpdateValidator()
        {
            RuleFor(x => x.ExerciseType).NotEmpty().WithMessage("Egzersiz türü boş bırakılamaz.");
            RuleFor(x => x.ExerciseType).Must(type => type == "System" || type == "Company").WithMessage("Egzersiz türü 'System' veya 'Company' olmalıdır.");
            RuleFor(x => x.ExerciseID).GreaterThan(0).WithMessage("Egzersiz seçimi yapılmalıdır.");
            RuleFor(x => x.OrderIndex).GreaterThan(0).WithMessage("Egzersiz sırası 1'den büyük olmalıdır.");
            RuleFor(x => x.Sets).GreaterThan(0).WithMessage("Set sayısı 1'den büyük olmalıdır.");
            RuleFor(x => x.Sets).LessThanOrEqualTo(20).WithMessage("Set sayısı 20'den fazla olamaz.");
            RuleFor(x => x.Reps).NotEmpty().WithMessage("Tekrar sayısı boş bırakılamaz.");
            RuleFor(x => x.Reps).MaximumLength(50).WithMessage("Tekrar sayısı en fazla 50 karakter olabilir.");
            RuleFor(x => x.RestTime).GreaterThanOrEqualTo(0).When(x => x.RestTime.HasValue).WithMessage("Dinlenme süresi negatif olamaz.");
            RuleFor(x => x.Notes).MaximumLength(500).WithMessage("Notlar en fazla 500 karakter olabilir.");
        }
    }
}
